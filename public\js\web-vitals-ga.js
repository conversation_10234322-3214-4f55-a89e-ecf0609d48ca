/**
 * Optimized Web Vitals tracking for Google Analytics
 * Self-contained module that doesn't require external CDN dependencies
 */

// Core Web Vitals implementation
(function() {
  'use strict';

  // Metric collection and reporting
  const metrics = new Map();
  let isReportingEnabled = false;

  // Check if gtag is available
  function isGtagAvailable() {
    return typeof window.gtag === 'function';
  }

  // Send metric to Google Analytics
  function sendToGA(metric) {
    if (!isGtagAvailable()) {
      console.warn('Web Vitals: gtag not available, queuing metric:', metric.name);
      return;
    }

    const value = metric.name === 'CLS' ? Math.round(metric.value * 1000) : Math.round(metric.value);
    
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: value,
      non_interaction: true,
      custom_parameter_1: metric.navigationType || 'unknown',
      custom_parameter_2: metric.rating || 'unknown'
    });
  }

  // Generate unique metric ID
  function generateId() {
    return 'v3-' + Date.now() + '-' + Math.floor(Math.random() * 1000000);
  }

  // Get navigation type
  function getNavigationType() {
    if ('navigation' in performance && performance.navigation) {
      const type = performance.navigation.type;
      switch (type) {
        case 0: return 'navigate';
        case 1: return 'reload';
        case 2: return 'back-forward';
        default: return 'unknown';
      }
    }
    return 'unknown';
  }

  // Rate metric performance
  function rateMetric(name, value) {
    const thresholds = {
      'CLS': [0.1, 0.25],
      'FID': [100, 300],
      'FCP': [1800, 3000],
      'LCP': [2500, 4000],
      'TTFB': [800, 1800]
    };

    const threshold = thresholds[name];
    if (!threshold) return 'unknown';

    if (value <= threshold[0]) return 'good';
    if (value <= threshold[1]) return 'needs-improvement';
    return 'poor';
  }

  // Create standardized metric object
  function createMetric(name, value, entries = []) {
    return {
      name,
      value,
      rating: rateMetric(name, value),
      id: generateId(),
      navigationType: getNavigationType(),
      entries
    };
  }

  // Report metric
  function reportMetric(metric) {
    if (isReportingEnabled) {
      sendToGA(metric);
    }
    metrics.set(metric.name, metric);
  }

  // Largest Contentful Paint (LCP)
  function observeLCP() {
    let metric;
    
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      metric = createMetric('LCP', lastEntry.startTime, [lastEntry]);
      reportMetric(metric);
    });

    observer.observe({ type: 'largest-contentful-paint', buffered: true });

    // Report final value on page hide
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden' && metric) {
        reportMetric(metric);
      }
    });
  }

  // First Contentful Paint (FCP)
  function observeFCP() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      
      if (fcpEntry) {
        const metric = createMetric('FCP', fcpEntry.startTime, [fcpEntry]);
        reportMetric(metric);
        observer.disconnect();
      }
    });

    observer.observe({ type: 'paint', buffered: true });
  }

  // Cumulative Layout Shift (CLS)
  function observeCLS() {
    let clsValue = 0;
    let sessionValue = 0;
    let sessionEntries = [];

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          const firstSessionEntry = sessionEntries[0];
          const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

          if (sessionValue && 
              entry.startTime - lastSessionEntry.startTime < 1000 &&
              entry.startTime - firstSessionEntry.startTime < 5000) {
            sessionValue += entry.value;
            sessionEntries.push(entry);
          } else {
            sessionValue = entry.value;
            sessionEntries = [entry];
          }

          if (sessionValue > clsValue) {
            clsValue = sessionValue;
            const metric = createMetric('CLS', clsValue, sessionEntries);
            reportMetric(metric);
          }
        }
      }
    });

    observer.observe({ type: 'layout-shift', buffered: true });

    // Report final value on page hide
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        const metric = createMetric('CLS', clsValue, sessionEntries);
        reportMetric(metric);
      }
    });
  }

  // First Input Delay (FID)
  function observeFID() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const metric = createMetric('FID', entry.processingStart - entry.startTime, [entry]);
        reportMetric(metric);
      }
    });

    observer.observe({ type: 'first-input', buffered: true });
  }

  // Time to First Byte (TTFB)
  function observeTTFB() {
    const navigationEntry = performance.getEntriesByType('navigation')[0];
    if (navigationEntry) {
      const metric = createMetric('TTFB', navigationEntry.responseStart, [navigationEntry]);
      reportMetric(metric);
    }
  }

  // Initialize all observers
  function initWebVitals() {
    // Enable reporting
    isReportingEnabled = true;

    // Check for required APIs
    if (!('PerformanceObserver' in window)) {
      console.warn('Web Vitals: PerformanceObserver not supported');
      return;
    }

    try {
      observeLCP();
      observeFCP();
      observeCLS();
      observeFID();
      observeTTFB();
      
      console.log('Web Vitals: Successfully initialized tracking');
    } catch (error) {
      console.warn('Web Vitals: Failed to initialize:', error);
    }
  }

  // Wait for gtag to be available, then initialize
  function waitForGtag() {
    if (isGtagAvailable()) {
      initWebVitals();
    } else {
      // Check again in 100ms
      setTimeout(waitForGtag, 100);
    }
  }

  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', waitForGtag);
  } else {
    waitForGtag();
  }

  // Expose for debugging
  window.webVitalsMetrics = metrics;
})();
