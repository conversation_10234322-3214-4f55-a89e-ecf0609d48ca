---
import { useTranslations } from '../i18n/utils';

export interface Props {
  currentLang: 'en' | 'pl';
}

const { currentLang } = Astro.props;
const t = useTranslations(currentLang);
---

<!-- Location Finder Component -->
<div
  x-data="locationFinder()"
  x-init="init()"
  @toggle-location-modal.window="toggleLocationModal()"
  class="location-finder"
>
  <!-- Location Button (Floating Action Button) -->
  <button
    @click="toggleLocationModal()"
    :disabled="isLoading || isCheckingPermissions"
    class="fixed bottom-6 right-6 z-40 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group"
    :class="{ 'animate-pulse': isLoading || isCheckingPermissions }"
    aria-label={t('geolocation.findNearbyRestAreas')}
    title={t('geolocation.findNearbyRestAreas')}
  >
    <!-- Location Icon -->
    <svg
      x-show="!isLoading && !isCheckingPermissions"
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6 group-hover:scale-110 transition-transform"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>

    <!-- Loading Spinner -->
    <svg
      x-show="isLoading || isCheckingPermissions"
      class="animate-spin h-6 w-6"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  </button>

  <!-- Location Modal -->
  <div
    x-show="showModal"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-50 overflow-y-auto"
    x-cloak
  >
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="closeModal()"
    ></div>

    <!-- Modal Content -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="relative w-full max-w-2xl bg-white dark:bg-secondary-900 rounded-lg shadow-xl"
      >
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700">
          <h3 class="text-lg font-semibold text-secondary-900 dark:text-white">
            <span x-text="modalTitle"></span>
          </h3>
          <button
            @click="closeModal()"
            class="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
          <!-- Initial Location Request -->
          <div x-show="currentState === 'initial'" class="text-center">
            <div class="mb-4">
              <svg class="mx-auto h-12 w-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <p class="text-secondary-600 dark:text-secondary-300 mb-6" x-text="requestLocationMessage"></p>
            <div class="flex gap-3 justify-center">
              <button
                @click="requestLocation()"
                class="btn-primary"
                x-text="allowLocationText"
              ></button>
              <button
                @click="closeModal()"
                class="btn-secondary"
                x-text="denyLocationText"
              ></button>
            </div>
          </div>

          <!-- Loading State -->
          <div x-show="currentState === 'loading'" class="text-center">
            <div class="mb-4">
              <svg class="animate-spin mx-auto h-12 w-12 text-primary-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <p class="text-secondary-600 dark:text-secondary-300" x-text="loadingMessage"></p>
          </div>

          <!-- Error State -->
          <div x-show="currentState === 'error'" class="text-center">
            <div class="mb-4">
              <svg class="mx-auto h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h4 class="text-lg font-medium text-red-600 mb-2" x-text="errorTitle"></h4>
            <p class="text-secondary-600 dark:text-secondary-300 mb-6" x-text="errorMessage"></p>
            
            <!-- Location Instructions for Permission Denied -->
            <div x-show="errorCode === 'PERMISSION_DENIED'" class="text-left bg-secondary-50 dark:bg-secondary-800 p-4 rounded-lg mb-4">
              <p class="font-medium text-secondary-900 dark:text-white mb-2" x-text="enableLocationInstructions"></p>
              <ol class="text-sm text-secondary-600 dark:text-secondary-300 space-y-1">
                <li x-text="enableLocationStep1"></li>
                <li x-text="enableLocationStep2"></li>
                <li x-text="enableLocationStep3"></li>
              </ol>
            </div>
            
            <button
              @click="retryLocationRequest()"
              class="btn-primary"
              :disabled="isLoading"
              x-text="retryText"
            ></button>
          </div>

          <!-- Results State -->
          <div x-show="currentState === 'results'">
            <div x-show="nearbyRestAreas.length === 0" class="text-center">
              <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
              </div>
              <p class="text-secondary-600 dark:text-secondary-300" x-text="noNearbyFoundText"></p>
            </div>
            
            <div x-show="nearbyRestAreas.length > 0">
              <p class="text-sm text-secondary-600 dark:text-secondary-300 mb-4">
                <span x-text="`${nearbyRestAreas.length} ${nearbyRestAreasText.toLowerCase()}`"></span>
              </p>
              
              <!-- Results List -->
              <div class="space-y-3 max-h-96 overflow-y-auto">
                <template x-for="restArea in nearbyRestAreas" :key="restArea.id">
                  <div class="border border-secondary-200 dark:border-secondary-700 rounded-lg p-4 hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors">
                    <div class="flex justify-between items-start mb-2">
                      <h4 class="font-medium text-secondary-900 dark:text-white" x-text="restArea.data.title"></h4>
                      <span class="text-sm text-primary-600 dark:text-primary-400 font-medium">
                        <span x-text="formatDistance(restArea.distance)"></span>
                        <span x-text="kmUnit"></span>
                        <span x-text="distanceAwayText"></span>
                      </span>
                    </div>
                    <p class="text-sm text-secondary-600 dark:text-secondary-300 mb-3" x-text="restArea.data.description_short"></p>

                    <!-- Amenity Icons -->
                    <div class="flex flex-wrap gap-2 mb-3" x-show="getKeyAmenities(restArea.data.amenities).length > 0">
                      <template x-for="(amenity, index) in getKeyAmenities(restArea.data.amenities).slice(0, 4)" :key="amenity.name">
                        <span
                          class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary-100 dark:bg-secondary-800 text-secondary-700 dark:text-secondary-300"
                          :title="amenity.label"
                        >
                          <span class="mr-1" x-text="amenity.icon"></span>
                          <span x-text="amenity.label" class="hidden sm:inline"></span>
                        </span>
                      </template>
                      <span
                        x-show="getKeyAmenities(restArea.data.amenities).length > 4"
                        class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary-100 dark:bg-secondary-800 text-secondary-700 dark:text-secondary-300"
                        x-text="`+${getKeyAmenities(restArea.data.amenities).length - 4} more`"
                      ></span>
                    </div>

                    <div class="flex gap-2">
                      <a
                        :href="getRestAreaUrl(restArea)"
                        class="text-sm btn-primary-sm"
                        x-text="viewDetailsText"
                      ></a>
                      <a
                        :href="getDirectionsUrl(restArea)"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-sm btn-secondary-sm"
                        x-text="getDirectionsText"
                      ></a>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  [x-cloak] { display: none !important; }
  
  .btn-primary-sm {
    padding: 0.5rem 1rem;
    background-color: var(--color-primary-600);
    color: white;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
  }

  .btn-primary-sm:hover {
    background-color: var(--color-primary-700);
  }

  .btn-secondary-sm {
    padding: 0.5rem 1rem;
    background-color: var(--color-secondary-200);
    color: var(--color-secondary-700);
    border-radius: 0.375rem;
    transition: background-color 0.2s;
  }

  .btn-secondary-sm:hover {
    background-color: var(--color-secondary-300);
  }

  .dark .btn-secondary-sm {
    background-color: var(--color-secondary-700);
    color: var(--color-secondary-300);
  }

  .dark .btn-secondary-sm:hover {
    background-color: var(--color-secondary-600);
  }
</style>

<script is:inline define:vars={{ translations: {
  findNearbyRestAreas: t('geolocation.findNearbyRestAreas'),
  requestLocationTitle: t('geolocation.requestLocationTitle'),
  requestLocationMessage: t('geolocation.requestLocationMessage'),
  allowLocation: t('geolocation.allowLocation'),
  denyLocation: t('geolocation.denyLocation'),
  locationDenied: t('geolocation.locationDenied'),
  locationDeniedMessage: t('geolocation.locationDeniedMessage'),
  locationError: t('geolocation.locationError'),
  locationErrorMessage: t('geolocation.locationErrorMessage'),
  locationUnavailable: t('geolocation.locationUnavailable'),
  locationUnavailableMessage: t('geolocation.locationUnavailableMessage'),
  gettingLocation: t('geolocation.gettingLocation'),
  searchingNearby: t('geolocation.searchingNearby'),
  nearbyRestAreas: t('geolocation.nearbyRestAreas'),
  noNearbyFound: t('geolocation.noNearbyFound'),
  distanceAway: t('geolocation.distanceAway'),
  kmUnit: t('geolocation.kmUnit'),
  retry: t('geolocation.retry'),
  enableLocationInstructions: t('geolocation.enableLocationInstructions'),
  enableLocationStep1: t('geolocation.enableLocationStep1'),
  enableLocationStep2: t('geolocation.enableLocationStep2'),
  enableLocationStep3: t('geolocation.enableLocationStep3'),
  closeModal: t('geolocation.closeModal'),
  viewDetails: t('geolocation.viewDetails'),
  getDirections: t('geolocation.getDirections')
}, currentLang }}>
  // Set global variables for the location finder
  window.translations = translations;
  window.currentLang = currentLang;

  // Debug logging
  console.log('LocationFinder: Initialized with currentLang:', currentLang);
  console.log('LocationFinder: window.currentLang set to:', window.currentLang);

  // Amenity icons and translations
  window.amenityIcons = {
    toilets: '🚻',
    wifi: '📶',
    fuel_station: '⛽',
    restaurant: '🍽️',
    shop: '🛒',
    playground: '🎠',
    showers: '🚿',
    car_wash: '🚗',
    ev_charging: '🔌',
    security: '👮',
    cctv: '📹',
    lighting: '💡',
    accommodation: '🏨',
    fenced_area: '🚧',
    vehicle_workshop: '🔧',
    liquid_waste_disposal_rv: '🚛',
    hydrogen_fueling: '⚡',
    cng_fueling: '🔥',
    lng_fueling: '❄️',
    snow_removal_ramp_trucks: '🚜',
    toilets_accessible: '♿',
    security_personnel_on_site: '👨‍✈️'
  };

  // Amenity translations
  window.amenityTranslations = {
    en: {
      toilets: 'Toilets',
      wifi: 'WiFi',
      fuel_station: 'Fuel Station',
      restaurant: 'Restaurant',
      shop: 'Shop',
      playground: 'Playground',
      showers: 'Showers',
      car_wash: 'Car Wash',
      ev_charging: 'EV Charging',
      security: 'Security',
      cctv: 'CCTV',
      lighting: 'Lighting',
      accommodation: 'Accommodation',
      fenced_area: 'Fenced Area',
      vehicle_workshop: 'Vehicle Workshop',
      liquid_waste_disposal_rv: 'RV Waste Disposal',
      hydrogen_fueling: 'Hydrogen Fueling',
      cng_fueling: 'CNG Fueling',
      lng_fueling: 'LNG Fueling',
      snow_removal_ramp_trucks: 'Snow Removal',
      toilets_accessible: 'Accessible Toilets',
      security_personnel_on_site: 'Security Personnel'
    },
    pl: {
      toilets: 'Toalety',
      wifi: 'WiFi',
      fuel_station: 'Stacja paliw',
      restaurant: 'Restauracja',
      shop: 'Sklep',
      playground: 'Plac zabaw',
      showers: 'Prysznice',
      car_wash: 'Myjnia samochodowa',
      ev_charging: 'Ładowanie EV',
      security: 'Ochrona',
      cctv: 'Monitoring',
      lighting: 'Oświetlenie',
      accommodation: 'Noclegi',
      fenced_area: 'Teren ogrodzony',
      vehicle_workshop: 'Warsztat',
      liquid_waste_disposal_rv: 'Utylizacja odpadów',
      hydrogen_fueling: 'Tankowanie wodoru',
      cng_fueling: 'Tankowanie CNG',
      lng_fueling: 'Tankowanie LNG',
      snow_removal_ramp_trucks: 'Odśnieżanie',
      toilets_accessible: 'Toalety dla niepełnosprawnych',
      security_personnel_on_site: 'Personel ochrony'
    }
  };

  // Inline geolocation utilities
  window.geolocationUtils = {
    // Calculate distance using Haversine formula
    calculateDistance: function(coord1, coord2) {
      const R = 6371; // Earth's radius in kilometers
      const dLat = this.toRadians(coord2.lat - coord1.lat);
      const dLon = this.toRadians(coord2.lon - coord1.lon);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.toRadians(coord1.lat)) * Math.cos(this.toRadians(coord2.lat)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;

      return Math.round(distance * 10) / 10;
    },

    toRadians: function(degrees) {
      return degrees * (Math.PI / 180);
    },

    formatDistance: function(distance) {
      return distance < 1 ? '< 1' : Math.round(distance).toString();
    },

    // Get current location
    getCurrentLocation: function() {
      return new Promise((resolve) => {
        if (!navigator.geolocation) {
          resolve({ success: false, error: 'Geolocation not supported', errorCode: 'NOT_SUPPORTED' });
          return;
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              success: true,
              coordinates: {
                lat: position.coords.latitude,
                lon: position.coords.longitude
              }
            });
          },
          (error) => {
            let errorCode = 'UNKNOWN';
            switch (error.code) {
              case error.PERMISSION_DENIED:
                errorCode = 'PERMISSION_DENIED';
                break;
              case error.POSITION_UNAVAILABLE:
                errorCode = 'POSITION_UNAVAILABLE';
                break;
              case error.TIMEOUT:
                errorCode = 'TIMEOUT';
                break;
            }
            resolve({ success: false, error: error.message, errorCode });
          },
          { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
        );
      });
    },

    // Find nearby rest areas
    findNearbyRestAreas: function(userLocation, restAreas, maxDistance = 50, maxResults = 10) {
      return restAreas
        .map(restArea => ({
          ...restArea,
          distance: this.calculateDistance(userLocation, restArea.data.coordinates)
        }))
        .filter(restArea => restArea.distance <= maxDistance)
        .sort((a, b) => a.distance - b.distance)
        .slice(0, maxResults);
    }
  };

  // Alpine.js locationFinder component
  window.locationFinder = function() {
    return {
      // State
      showModal: false,
      isLoading: false,
      isCheckingPermissions: false,
      currentState: 'initial',
      nearbyRestAreas: [],
      userLocation: null,
      errorCode: null,

      // Translations
      modalTitle: translations.findNearbyRestAreas,
      requestLocationMessage: translations.requestLocationMessage,
      allowLocationText: translations.allowLocation,
      denyLocationText: translations.denyLocation,
      loadingMessage: translations.gettingLocation,
      errorTitle: translations.locationError,
      errorMessage: translations.locationErrorMessage,
      nearbyRestAreasText: translations.nearbyRestAreas,
      noNearbyFoundText: translations.noNearbyFound,
      distanceAwayText: translations.distanceAway,
      kmUnit: translations.kmUnit,
      retryText: translations.retry,
      enableLocationInstructions: translations.enableLocationInstructions,
      enableLocationStep1: translations.enableLocationStep1,
      enableLocationStep2: translations.enableLocationStep2,
      enableLocationStep3: translations.enableLocationStep3,
      viewDetailsText: translations.viewDetails,
      getDirectionsText: translations.getDirections,

      // Initialize
      async init() {
        // Check existing permissions first
        await this.checkExistingPermissions();
        // Set up permission monitoring
        this.setupPermissionMonitoring();
        // Check for automatic prompt on first visit
        this.checkForAutomaticPrompt();
      },

      // Set up permission state monitoring
      setupPermissionMonitoring() {
        if ('permissions' in navigator) {
          navigator.permissions.query({ name: 'geolocation' }).then(permission => {
            // Listen for permission changes
            permission.addEventListener('change', () => {
              console.log('LocationFinder: Permission state changed to:', permission.state);

              if (permission.state === 'denied') {
                // Permission was revoked
                localStorage.setItem('location-permission-denied', 'true');
                this.userLocation = null;
              } else if (permission.state === 'granted') {
                // Permission was granted
                localStorage.removeItem('location-permission-denied');
                // Optionally attempt silent location detection
                this.attemptSilentLocationDetection();
              }
            });
          }).catch(error => {
            console.log('LocationFinder: Error setting up permission monitoring:', error);
          });
        }
      },

      // Check existing location permissions
      async checkExistingPermissions() {
        // Skip if geolocation is not supported
        if (!navigator.geolocation) {
          return;
        }

        this.isCheckingPermissions = true;

        try {
          // Check if Permissions API is supported
          if ('permissions' in navigator) {
            const permission = await navigator.permissions.query({ name: 'geolocation' });

            if (permission.state === 'granted') {
              // Permission already granted, try to get location silently
              console.log('LocationFinder: Permission already granted, attempting automatic location detection');
              await this.attemptSilentLocationDetection();
            } else if (permission.state === 'denied') {
              // Permission was denied, update localStorage
              localStorage.setItem('location-permission-denied', 'true');
              console.log('LocationFinder: Permission was denied');
            }
            // If state is 'prompt', we'll handle it through normal flow
          } else {
            // Fallback for browsers without Permissions API
            // Try to detect based on localStorage flags
            const permissionDenied = localStorage.getItem('location-permission-denied');
            if (!permissionDenied) {
              // Attempt silent detection if no previous denial
              await this.attemptSilentLocationDetection();
            }
          }
        } catch (error) {
          console.log('LocationFinder: Error checking permissions:', error);
          // Silently fail and continue with normal flow
        } finally {
          this.isCheckingPermissions = false;
        }
      },

      // Attempt to get location silently (without showing UI)
      async attemptSilentLocationDetection() {
        try {
          const result = await window.geolocationUtils.getCurrentLocation();

          if (result.success) {
            this.userLocation = result.coordinates;
            console.log('LocationFinder: Silent location detection successful');
            // Don't automatically show modal or search - wait for user interaction
            // But store the location for when they do open the modal
          } else if (result.errorCode === 'PERMISSION_DENIED') {
            // Permission was revoked, update localStorage
            localStorage.setItem('location-permission-denied', 'true');
            console.log('LocationFinder: Permission was revoked');
          }
        } catch (error) {
          console.log('LocationFinder: Silent location detection failed:', error);
          // Silently fail - this is expected for first-time users
        }
      },

      // Check for automatic prompt
      checkForAutomaticPrompt() {
        const hasPrompted = localStorage.getItem('location-prompted');
        const permissionDenied = localStorage.getItem('location-permission-denied');
        const lastPromptTime = localStorage.getItem('location-last-prompt');

        // Don't prompt if:
        // - Already prompted in this session
        // - Permission was previously denied
        // - Geolocation not supported
        // - Prompted recently (within 24 hours)
        if (hasPrompted || permissionDenied || !navigator.geolocation) {
          return;
        }

        if (lastPromptTime) {
          const timeSinceLastPrompt = Date.now() - parseInt(lastPromptTime);
          const twentyFourHours = 24 * 60 * 60 * 1000;
          if (timeSinceLastPrompt < twentyFourHours) {
            return;
          }
        }

        // Show prompt after a delay to avoid blocking page load
        setTimeout(() => {
          this.showModal = true;
          this.currentState = 'initial';
          localStorage.setItem('location-prompted', 'true');
          localStorage.setItem('location-last-prompt', Date.now().toString());
        }, 3000); // 3 second delay
      },

      // Toggle modal
      async toggleLocationModal() {
        this.showModal = !this.showModal;
        if (this.showModal) {
          // Check if we already have location from silent detection
          if (this.userLocation) {
            console.log('LocationFinder: Using existing location data');
            this.currentState = 'loading';
            this.isLoading = true;
            await this.searchNearbyRestAreas();
            this.isLoading = false;
          } else {
            // Show loading state while checking permissions
            this.currentState = 'loading';
            this.loadingMessage = 'Checking location permissions...';

            // Check permissions again in case they changed
            await this.checkExistingPermissions();

            if (this.userLocation) {
              // Location was obtained during permission check
              console.log('LocationFinder: Location obtained during permission check');
              this.isLoading = true;
              this.loadingMessage = translations.searchingNearby;
              await this.searchNearbyRestAreas();
              this.isLoading = false;
            } else {
              // No location available, show initial state
              this.currentState = 'initial';
            }
          }
        }
      },

      // Close modal
      closeModal() {
        this.showModal = false;
        this.currentState = 'initial';
        this.isLoading = false;
        this.nearbyRestAreas = [];
        this.errorCode = null;
      },

      // Request location
      async requestLocation() {
        this.isLoading = true;
        this.currentState = 'loading';

        // Clear previous permission denial when user manually tries
        localStorage.removeItem('location-permission-denied');

        try {
          const result = await window.geolocationUtils.getCurrentLocation();

          if (result.success) {
            this.userLocation = result.coordinates;
            // Clear permission denial flag on success
            localStorage.removeItem('location-permission-denied');
            await this.searchNearbyRestAreas();
          } else {
            this.showError(result.errorCode, result.error);
          }
        } catch (error) {
          this.showError('UNKNOWN', error.message);
        } finally {
          this.isLoading = false;
        }
      },

      // Search nearby rest areas
      async searchNearbyRestAreas() {
        this.loadingMessage = translations.searchingNearby;

        try {
          // Fetch rest areas data from API using the current language
          const currentLanguage = currentLang || 'en';
          const apiUrl = `/api/rest-areas.json?lang=${currentLanguage}`;
          console.log('LocationFinder: Using language:', currentLanguage);
          console.log('LocationFinder: currentLang prop:', currentLang);
          console.log('LocationFinder: API URL:', apiUrl);
          const response = await fetch(apiUrl, { headers: { 'x-current-lang': currentLanguage } });
          if (!response.ok) {
            throw new Error('Failed to fetch rest areas');
          }

          let restAreas = await response.json();

          // Fallback: if we're on Polish but got EN entries, retry explicitly with lang=pl
          if (currentLanguage === 'pl' && Array.isArray(restAreas) && restAreas.length > 0 && restAreas.every((ra) => typeof ra.id === 'string' && ra.id.startsWith('en/'))) {
            try {
              const retryResp = await fetch('/api/rest-areas.json?lang=pl', { headers: { 'x-current-lang': 'pl' } });
              if (retryResp.ok) {
                const retryData = await retryResp.json();
                if (Array.isArray(retryData) && retryData.length > 0 && retryData.some((ra) => typeof ra.id === 'string' && ra.id.startsWith('pl/'))) {
                  restAreas = retryData;
                }
              }
            } catch (e) {
              console.warn('LocationFinder: retry for pl failed', e);
            }
          }

          // Strictly filter by the current language to avoid mixed/EN entries on PL
          if (Array.isArray(restAreas)) {
            restAreas = restAreas.filter((ra) => typeof ra.id === 'string' && ra.id.startsWith(`${currentLanguage}/`));
          }

          // If nothing after filtering and we're on PL, gracefully fall back to EN data
          if ((!restAreas || restAreas.length === 0) && currentLanguage === 'pl') {
            try {
              const enResp = await fetch('/api/rest-areas.json?lang=en', { headers: { 'x-current-lang': 'en' } });
              if (enResp.ok) {
                restAreas = await enResp.json();
              }
            } catch (e) {
              console.warn('LocationFinder: EN fallback fetch failed', e);
            }
          }

          if (this.userLocation && restAreas.length > 0) {
            this.nearbyRestAreas = window.geolocationUtils.findNearbyRestAreas(
              this.userLocation,
              restAreas,
              50,
              10
            );
            this.currentState = 'results';
          } else {
            this.showError('NO_DATA', 'No rest areas found');
          }
        } catch (error) {
          console.error('Error fetching rest areas:', error);
          this.showError('FETCH_ERROR', 'Failed to load rest areas data');
        }
      },

      // Show error
      showError(errorCode, message) {
        this.currentState = 'error';
        this.errorCode = errorCode;

        switch (errorCode) {
          case 'PERMISSION_DENIED':
            this.errorTitle = translations.locationDenied;
            this.errorMessage = translations.locationDeniedMessage;
            // Track permission denial to avoid future prompts
            localStorage.setItem('location-permission-denied', 'true');
            // Clear any cached location data since permission was denied
            this.userLocation = null;
            break;
          case 'POSITION_UNAVAILABLE':
            this.errorTitle = translations.locationUnavailable;
            this.errorMessage = translations.locationUnavailableMessage;
            break;
          case 'FETCH_ERROR':
            this.errorTitle = translations.locationError;
            this.errorMessage = 'Unable to load rest areas data. Please check your internet connection and try again.';
            break;
          default:
            this.errorTitle = translations.locationError;
            this.errorMessage = message || translations.locationErrorMessage;
        }
      },

      // Retry location request
      async retryLocationRequest() {
        // Clear any previous errors
        this.errorCode = null;

        // Check if permission was previously denied
        const permissionDenied = localStorage.getItem('location-permission-denied');
        if (permissionDenied) {
          // Clear the denial flag to allow retry
          localStorage.removeItem('location-permission-denied');
        }

        // Clear cached location to force fresh request
        this.userLocation = null;

        // Attempt location request
        await this.requestLocation();
      },

      // Format distance
      formatDistance(distance) {
        return window.geolocationUtils.formatDistance(distance);
      },

      // Get rest area URL
      getRestAreaUrl(restArea) {
        // Extract slug by removing any language prefix (en/ or pl/) and .md extension
        let slug = restArea.id;
        if (slug.startsWith('en/')) {
          slug = slug.substring(3);
        } else if (slug.startsWith('pl/')) {
          slug = slug.substring(3);
        }
        slug = slug.replace(/\.md$/, '');

        return currentLang === 'en' ? `/rest-areas/${slug}` : `/${currentLang}/rest-areas/${slug}`;
      },

      // Get directions URL
      getDirectionsUrl(restArea) {
        const coords = restArea.data.coordinates;
        return `https://www.google.com/maps/dir/?api=1&destination=${coords.lat},${coords.lon}`;
      },

      // Get key amenities for display
      getKeyAmenities(amenities) {
        // Define priority order for key amenities
        const keyAmenityOrder = [
          'toilets', 'fuel_station', 'restaurant', 'ev_charging',
          'showers', 'wifi', 'shop', 'accommodation', 'security',
          'playground', 'cctv', 'lighting'
        ];

        const currentLang = window.currentLang || 'en';
        const icons = window.amenityIcons || {};
        const translations = window.amenityTranslations?.[currentLang] || {};

        return keyAmenityOrder
          .filter(amenityKey => amenities[amenityKey])
          .map(amenityKey => ({
            name: amenityKey,
            icon: icons[amenityKey] || '•',
            label: translations[amenityKey] || amenityKey
          }));
      }
    };
  };
</script>
