---
import Layout from '../../../layouts/Layout.astro';
import { getCollection, render } from 'astro:content';
import AmenityIcon from '../../../components/AmenityIcon.astro';
import RestAreaCard from '../../../components/RestAreaCard.astro';
import Breadcrumbs from '../../../components/Breadcrumbs.astro';
import ParkingBadges from '../../../components/ParkingBadges.astro';
import GoogleMap from '../../../components/GoogleMap.astro';
import locationsData from '../../../data/locations_pl.json';
import { generateRestAreaStructuredData, generateBreadcrumbStructuredData } from '../../../utils/structuredData.ts';
import { getLangFromUrl, useTranslations } from '../../../i18n/utils';

export async function getStaticPaths() {
  // Fetch Polish entries. IDs are like 'pl/some-area.md'.
  const polishEntries = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));

  return polishEntries.map((entry) => {
    // entry.data.location_path is like "poland/region/city" or "polska/region/city"
    // entry.id is like "pl/rest-area-name.md"
    // Ensure location path for slug uses 'polska' if this page is under /pl/polska
    const locationPathForSlug = entry.data.location_path.replace(/^(poland|polska)\//, ''); // "region/city"
    const baseNameSlug = entry.id.substring(3).replace(/\.md$/, ''); // "rest-area-name"
    
    const hierarchicalSlug = `${locationPathForSlug}/${baseNameSlug}`; // "region/city/rest-area-name"

    return {
      params: { restAreaSlug: hierarchicalSlug },
      props: { restArea: entry },
    };
  });
}

const { restArea } = Astro.props; // This is a Polish entry
const { restAreaSlug } = Astro.params; // This is "region/city/rest-area-name"
const { Content } = await render(restArea);

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Parse work hours
function parseWorkHours(workHours: string) {
  try {
    return JSON.parse(workHours);
  } catch {
    return {};
  }
}

// Parse contact info
function parseContactInfo(contactInfo: string) {
  try {
    return JSON.parse(contactInfo);
  } catch {
    return {};
  }
}

const workHours = parseWorkHours(restArea.data.work_hours);
const contactInfo = parseContactInfo(restArea.data.contact_info || '{}');

// Parse the hierarchical slug to get location path
const slugParts = restAreaSlug.split('/');
const locationParts = slugParts.slice(0, -1);

// Build location hierarchy for breadcrumbs
function findLocationInHierarchy(locations: any[], targetSlug: string, path: any[] = []): any[] | null {
  for (const location of locations) {
    const currentPath = [...path, location];

    if (location.slug === targetSlug) {
      return currentPath;
    }

    if (location.children) {
      const result = findLocationInHierarchy(location.children, targetSlug, currentPath);
      if (result) return result;
    }
  }
  return null;
}

// Build breadcrumbs
const breadcrumbs = [{ label: t('nav.home'), href: '/pl/' }];
breadcrumbs.push({ label: t('countries.PL'), href: '/pl/polska/' });

let currentPath = '/pl/polska';
for (const locationSlug of locationParts) {
  // Find the location in the hierarchy
  const locationPath = findLocationInHierarchy(locationsData, locationSlug);
  if (locationPath) {
    const location = locationPath[locationPath.length - 1];
    currentPath += `/${locationSlug}`;
    breadcrumbs.push({
      label: location.name,
      href: `${currentPath}/`
    });
  }
}

// Add current rest area to breadcrumbs
breadcrumbs.push({
  label: restArea.data.title,
  href: `/pl/polska/${restAreaSlug}`
});

// Get location info for page title
const region = locationParts.length > 0 ? findLocationInHierarchy(locationsData, locationParts[0])?.slice(-1)[0] : null;
const branch = locationParts.length > 1 ? findLocationInHierarchy(locationsData, locationParts[1])?.slice(-1)[0] : null;

// Get nearby rest areas (same location) - filter for Polish content only
const polishRestAreasForNearby = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));

const nearbyRestAreas = polishRestAreasForNearby
  .filter((area) =>
    area.id !== restArea.id && // Compare full entry IDs
    area.data.location_path === restArea.data.location_path // Ensure same location_path
  )
  .slice(0, 3);

const pageTitle = `${restArea.data.title} - ${t('restArea.title')} ${branch?.name || region?.name || t('countries.PL')}`; // Use translated country name

// Generate structured data for Polish version
const restAreaUrl = new URL(`/pl/polska/${restAreaSlug}`, Astro.site).href;
const restAreaStructuredData = generateRestAreaStructuredData(restArea.data, restAreaUrl);
const breadcrumbStructuredData = generateBreadcrumbStructuredData(breadcrumbs);

// Combine structured data
const combinedStructuredData = [restAreaStructuredData, breadcrumbStructuredData];
---

<Layout
  title={pageTitle}
  description={restArea.data.description_short}
  type="place"
  image={restArea.data.featured_image}
  imageAlt={restArea.data.title}
  structuredData={combinedStructuredData}
>
  <main>
    <div class="container-custom pt-8">
      <Breadcrumbs items={breadcrumbs} />

      <!-- Hero Section -->
      <header class="mb-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          <div>
            <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
              {restArea.data.title}
            </h1>
            <div class="flex flex-wrap items-center gap-4 mb-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                {restArea.data.highway_tag.startsWith('A') ? `${t('restArea.highway')} ${restArea.data.highway_tag}` :
                 restArea.data.highway_tag.startsWith('S') ? `${t('restArea.expressway')} ${restArea.data.highway_tag}` :
                 restArea.data.highway_tag}
              </span>
              {restArea.data.travel_direction && (
                <span class="text-xl text-secondary-600 dark:text-secondary-300">
                  {t('restArea.direction')}: {restArea.data.travel_direction}
                </span>
              )}
            </div>

            <!-- Parking Spaces - Desktop Only -->
            <div class="mb-6 mt-8 hidden md:block">
              <h3 class="text-lg font-semibold text-secondary-900 dark:text-white mb-3">{t('restArea.parkingSpaces')}</h3>
              <ParkingBadges
                parkingSpacesCars={restArea.data.parking_spaces_cars}
                parkingSpacesTrucks={restArea.data.parking_spaces_trucks}
                parkingSpacesBuses={restArea.data.parking_spaces_buses}
                parkingSpacesDangerous={restArea.data.parking_spaces_dangerous}
              />
            </div>
          </div>

          <div class="relative">
            <GoogleMap
              lat={restArea.data.coordinates.lat}
              lon={restArea.data.coordinates.lon}
              title={restArea.data.title}
              address={restArea.data.address_line}
              className="w-full h-64 lg:h-80 rounded-xl shadow-lg"
              rating={restArea.data.rating}
            />
          </div>
        </div>
      </header>

      <!-- Parking Spaces - Mobile Only -->
      <section class="mb-8 md:hidden">
        <h2 class="text-2xl font-semibold mb-6">{t('restArea.parkingSpaces')}</h2>
        <ParkingBadges
          parkingSpacesCars={restArea.data.parking_spaces_cars}
          parkingSpacesTrucks={restArea.data.parking_spaces_trucks}
          parkingSpacesBuses={restArea.data.parking_spaces_buses}
          parkingSpacesDangerous={restArea.data.parking_spaces_dangerous}
        />
      </section>

      <!-- Available Amenities -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">{t('restArea.amenities')}</h2>

        <!-- Primary Amenities -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
          {(() => {
            const secondaryAmenities = ['fenced_area', 'vehicle_workshop', 'liquid_waste_disposal_rv', 'hydrogen_fueling', 'cng_fueling', 'lng_fueling', 'snow_removal_ramp_trucks', 'cctv', 'lighting', 'car_wash'];

            // Define custom order for primary amenities
            const amenityOrder = [
              'toilets', 'showers', 'fuel_station', 'ev_charging',
              'restaurant', 'shop', 'playground', 'accommodation', 'wifi', 'security'
            ];

            // Get available primary amenities in custom order
            const orderedAmenities = amenityOrder
              .filter(amenity =>
                (restArea.data.amenities as any)[amenity] &&
                !secondaryAmenities.includes(amenity)
              )
              .map(amenity => [amenity, (restArea.data.amenities as any)[amenity]]);

            return orderedAmenities.map(([amenity, isAvailable]) => (
              <AmenityIcon
                amenityName={amenity}
                isPresent={isAvailable as boolean}
                size="md"
                showLabel={true}
                variant="primary"
              />
            ));
          })()}
        </div>
      </section>

      <!-- Secondary Amenities -->
      <section class="mb-12">
        <div class="border-t border-secondary-200 dark:border-secondary-700 pt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 overflow-hidden">
            <!-- Security & Safety -->
            {(restArea.data.amenities.fenced_area || restArea.data.security_personnel_on_site || restArea.data.amenities.cctv || restArea.data.amenities.lighting) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">{t('amenityCategories.security')}</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.fenced_area && (
                    <AmenityIcon
                      amenityName="fenced_area"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.security_personnel_on_site && (
                    <AmenityIcon
                      amenityName="security_personnel_on_site"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.cctv && (
                    <AmenityIcon
                      amenityName="cctv"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.lighting && (
                    <AmenityIcon
                      amenityName="lighting"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                </div>
              </div>
            )}

            <!-- Alternative Fuels -->
            {(restArea.data.amenities.hydrogen_fueling || restArea.data.amenities.cng_fueling || restArea.data.amenities.lng_fueling) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">{t('amenityCategories.alternativeFuels')}</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.hydrogen_fueling && (
                    <AmenityIcon
                      amenityName="hydrogen_fueling"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.cng_fueling && (
                    <AmenityIcon
                      amenityName="cng_fueling"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.lng_fueling && (
                    <AmenityIcon
                      amenityName="lng_fueling"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                </div>
              </div>
            )}

            <!-- Specialized Services -->
            {(restArea.data.amenities.vehicle_workshop || restArea.data.amenities.liquid_waste_disposal_rv || restArea.data.amenities.snow_removal_ramp_trucks || restArea.data.amenities.car_wash) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">{t('amenityCategories.specializedServices')}</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.vehicle_workshop && (
                    <AmenityIcon
                      amenityName="vehicle_workshop"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.liquid_waste_disposal_rv && (
                    <AmenityIcon
                      amenityName="liquid_waste_disposal_rv"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.snow_removal_ramp_trucks && (
                    <AmenityIcon
                      amenityName="snow_removal_ramp_trucks"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                  {restArea.data.amenities.car_wash && (
                    <AmenityIcon
                      amenityName="car_wash"
                      isPresent={true}
                      size="sm"
                      showLabel={true}
                      variant="secondary"
                    />
                  )}
                </div>
              </div>
            )}

            <!-- Accessibility -->
            {restArea.data.toilets_accessible && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">{t('amenityCategories.accessibility')}</h4>
                <div class="space-y-2">
                  <AmenityIcon
                    amenityName="toilets_accessible"
                    isPresent={true}
                    size="sm"
                    showLabel={true}
                    variant="secondary"
                  />
                </div>
              </div>
            )}

          </div>
        </div>
      </section>

      <!-- Main Content -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Sidebar - appears first on mobile, last on desktop -->
        <aside class="space-y-6 order-1 lg:order-2">
          <!-- Map Link -->
          {restArea.data.maps_url && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">{t('restArea.location')}</h3>
              <a
                href={restArea.data.maps_url}
                target="_blank"
                rel="noopener noreferrer"
                class="btn-primary w-full text-center"
              >
                {t('restArea.viewOnMap')}
              </a>
            </div>
          )}

          <!-- Quick Info -->
          <div class="card p-6">
            <h3 class="text-xl font-semibold mb-4">{t('restArea.quickInfo')}</h3>
            <div class="space-y-3">
              <div>
                <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">{t('restArea.address')}:</span>
                <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.address_line}</p>
              </div>

              {restArea.data.administrator && (
                <div>
                  <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">{t('restArea.operator')}:</span>
                  <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.administrator}</p>
                </div>
              )}

              {restArea.data.mop_category && (
                <div>
                  <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">{t('restArea.category')}:</span>
                  <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.mop_category}</p>
                </div>
              )}
            </div>
          </div>

          <!-- Working Hours -->
          {Object.keys(workHours).length > 0 && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">{t('restArea.hours')}</h3>
              <div class="space-y-2">
                {Object.entries(workHours).map(([day, hours]) => (
                  <div class="flex justify-between">
                    <span class="capitalize text-secondary-600 dark:text-secondary-400">{t(`days.${day}`)}:</span>
                    <span class="text-secondary-900 dark:text-secondary-100">{hours as string}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <!-- Contact Info - Disabled for future app version -->
          {false && Object.keys(contactInfo).length > 0 && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">{t('restArea.contact')}</h3>
              <div class="space-y-2">
                {contactInfo.phone && (
                  <div>
                    <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Telefon:</span>
                    <p class="text-secondary-900 dark:text-secondary-100">{contactInfo.phone}</p>
                  </div>
                )}
                {contactInfo.email && (
                  <div>
                    <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Email:</span>
                    <p class="text-secondary-900 dark:text-secondary-100">{contactInfo.email}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </aside>

        <!-- Content - appears second on mobile, first on desktop -->
        <article class="lg:col-span-2 order-2 lg:order-1">
          <div class="prose prose-lg dark:prose-invert max-w-none [&>p]:mb-6">
            <Content />
          </div>
        </article>
      </div>

      <!-- Nearby Rest Areas -->
      {nearbyRestAreas.length > 0 && (
        <section class="mt-12">
          <h2 class="text-2xl font-semibold mb-6">
            {t('restArea.nearbyRestAreas')} w {branch?.name || region?.name}
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {nearbyRestAreas.map((area: any) => (
              <RestAreaCard restArea={area} />
            ))}
          </div>
        </section>
      )}
    </div>
  </main>

  <!-- Spacing before footer -->
  <div class="pb-12"></div>
</Layout>
