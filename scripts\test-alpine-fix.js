/**
 * Test script to verify Alpine.js initialization fix
 * Run this in the browser console to check if Alpine.js components are working correctly
 */

function testAlpineJsComponents() {
  console.log('🧪 Testing Alpine.js Components After Fix');
  console.log('==========================================');

  // Check if Alpine.js is loaded
  if (typeof Alpine === 'undefined') {
    console.error('❌ Alpine.js is not loaded');
    return;
  }
  console.log('✅ Alpine.js is loaded');

  // Test 1: Check for JavaScript errors in console
  console.log('\n1. Checking for JavaScript errors...');
  const originalError = console.error;
  let errorCount = 0;
  console.error = function(...args) {
    if (args[0] && args[0].includes && args[0].includes('init is not defined')) {
      errorCount++;
      console.log('❌ Found "init is not defined" error:', args[0]);
    }
    originalError.apply(console, args);
  };

  // Test 2: Check search form functionality
  console.log('\n2. Testing search form components...');
  const searchForms = document.querySelectorAll('[data-search-form]');
  if (searchForms.length > 0) {
    console.log(`✅ Found ${searchForms.length} search form(s)`);
    
    searchForms.forEach((form, index) => {
      try {
        const alpineData = Alpine.$data(form);
        if (alpineData) {
          console.log(`✅ Search form ${index + 1} Alpine.js data accessible:`, {
            activeTab: alpineData.activeTab,
            regionsCount: alpineData.regions?.length || 0,
            highwaysCount: alpineData.highways?.length || 0
          });
        } else {
          console.log(`❌ Search form ${index + 1} Alpine.js data not accessible`);
        }
      } catch (error) {
        console.log(`❌ Error accessing search form ${index + 1} data:`, error.message);
      }
    });
  } else {
    console.log('ℹ️ No search forms found on this page');
  }

  // Test 3: Check dark mode functionality
  console.log('\n3. Testing dark mode functionality...');
  const htmlElement = document.documentElement;
  if (htmlElement.hasAttribute('x-data')) {
    try {
      const alpineData = Alpine.$data(htmlElement);
      if (alpineData && typeof alpineData.darkMode !== 'undefined') {
        console.log('✅ Dark mode Alpine.js data accessible:', {
          darkMode: alpineData.darkMode,
          currentTheme: htmlElement.classList.contains('dark') ? 'dark' : 'light'
        });
      } else {
        console.log('❌ Dark mode Alpine.js data not accessible');
      }
    } catch (error) {
      console.log('❌ Error accessing dark mode data:', error.message);
    }
  } else {
    console.log('ℹ️ No dark mode functionality found');
  }

  // Test 4: Check language selector
  console.log('\n4. Testing language selector...');
  const languageSelector = document.querySelector('[x-data*="languageSelector"]');
  if (languageSelector) {
    try {
      const alpineData = Alpine.$data(languageSelector);
      if (alpineData) {
        console.log('✅ Language selector Alpine.js data accessible:', {
          isOpen: alpineData.isOpen
        });
      } else {
        console.log('❌ Language selector Alpine.js data not accessible');
      }
    } catch (error) {
      console.log('❌ Error accessing language selector data:', error.message);
    }
  } else {
    console.log('ℹ️ No language selector found on this page');
  }

  // Test 5: Check cookie consent banner
  console.log('\n5. Testing cookie consent banner...');
  const cookieConsent = document.querySelector('#cookie-consent-banner');
  if (cookieConsent) {
    try {
      const alpineData = Alpine.$data(cookieConsent);
      if (alpineData) {
        console.log('✅ Cookie consent Alpine.js data accessible:', {
          consentGiven: alpineData.consentGiven,
          showDetails: alpineData.showDetails
        });
      } else {
        console.log('❌ Cookie consent Alpine.js data not accessible');
      }
    } catch (error) {
      console.log('❌ Error accessing cookie consent data:', error.message);
    }
  } else {
    console.log('ℹ️ No cookie consent banner found on this page');
  }

  // Test 6: Check filter components (on rest areas pages)
  console.log('\n6. Testing filter components...');
  const filterComponents = document.querySelectorAll('[x-data*="searchTerm"]');
  if (filterComponents.length > 0) {
    console.log(`✅ Found ${filterComponents.length} filter component(s)`);
    
    filterComponents.forEach((component, index) => {
      try {
        const alpineData = Alpine.$data(component);
        if (alpineData) {
          console.log(`✅ Filter component ${index + 1} Alpine.js data accessible:`, {
            searchTerm: alpineData.searchTerm,
            selectedHighway: alpineData.selectedHighway,
            highwaysCount: alpineData.highways?.length || 0
          });
        } else {
          console.log(`❌ Filter component ${index + 1} Alpine.js data not accessible`);
        }
      } catch (error) {
        console.log(`❌ Error accessing filter component ${index + 1} data:`, error.message);
      }
    });
  } else {
    console.log('ℹ️ No filter components found on this page');
  }

  // Final summary
  console.log('\n📊 Test Summary');
  console.log('================');
  if (errorCount === 0) {
    console.log('✅ No "init is not defined" errors detected');
  } else {
    console.log(`❌ Found ${errorCount} "init is not defined" error(s)`);
  }

  // Restore original console.error
  console.error = originalError;

  console.log('\n🎯 Next Steps:');
  console.log('1. Check browser console for any remaining JavaScript errors');
  console.log('2. Test interactive elements (search forms, dark mode toggle, language selector)');
  console.log('3. Navigate between pages to ensure Alpine.js works consistently');
  console.log('4. Test on different browsers and devices');
}

// Auto-run the test
if (typeof window !== 'undefined') {
  // Wait for Alpine.js to initialize
  document.addEventListener('alpine:init', () => {
    setTimeout(testAlpineJsComponents, 1000);
  });

  // Fallback if Alpine.js is already initialized
  if (window.Alpine) {
    setTimeout(testAlpineJsComponents, 1000);
  }

  // Make function globally available for manual testing
  window.testAlpineJsComponents = testAlpineJsComponents;
}

// Export for Node.js environments
if (typeof module !== 'undefined') {
  module.exports = { testAlpineJsComponents };
}
