#!/usr/bin/env node

/**
 * Enhanced Markdown Generation Script
 * 
 * This script generates markdown files from JSON data (either from Google Sheets export
 * or CSV conversion). It maintains compatibility with the existing Content Layer API
 * schema and supports both English and Polish content generation.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  inputDir: path.join(__dirname, '../temp/sheets-export'),
  outputDirs: {
    en: path.join(__dirname, '../src/content/rest-areas/en'),
    pl: path.join(__dirname, '../src/content/rest-areas/pl')
  },
  backupDir: path.join(__dirname, '../temp/content-backup'),
  // Schema validation based on src/content/config.ts
  requiredFields: [
    'title', 'publishedDate', 'description_short', 'address_line',
    'coordinates', 'work_hours', 'country_code', 'location_path', 'highway_tag'
  ]
};

// Helper function to slugify strings (same as existing script)
function slugify(text) {
  if (!text) return '';
  return text
    .toString()
    .toLowerCase()
    .replace(/ł/g, 'l')
    .replace(/ć/g, 'c')
    .replace(/ó/g, 'o')
    .replace(/ę/g, 'e')
    .replace(/ą/g, 'a')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
}

// Helper function to format dates to YYYY-MM-DD
function formatDate(dateString) {
  let date;
  if (dateString && dateString.trim() !== '') {
    date = new Date(dateString);
    if (isNaN(date.getTime())) {
      date = new Date(); // Use current date if invalid
    }
  } else {
    date = new Date(); // Use current date if no dateString provided
  }
  return date.toISOString().split('T')[0];
}

// Convert string boolean values to actual booleans
function parseBoolean(value) {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim();
    return ['yes', 'true', '1', 'tak'].includes(lower);
  }
  return false;
}

// Generate YAML frontmatter compatible with Content Layer API schema
function generateFrontmatter(data, language = 'en') {
  const title = data.title || `${data.rest_area_id} Rest Area`;
  const description = data.description_short ||
    `Rest area ${data.rest_area_id} on ${data.road_class}${data.road_number} road in Poland.`;

  const addressLine = data.address_line ||
    `${data.road_class}${data.road_number}, ${data.km_marker}, ${data.location}, ${data.region}`;

  // Parse coordinates (note: in the exported data, longitude and latitude are swapped)
  // Fix: swap the values to correct the coordinate reversal issue
  const lat = parseFloat(data.longitude) || 0;  // Use longitude value for latitude
  const lon = parseFloat(data.latitude) || 0;   // Use latitude value for longitude

  // Generate work hours (default to 24/7)
  const workHours = data.work_hours ||
    '{"monday": "00:00-24:00", "tuesday": "00:00-24:00", "wednesday": "00:00-24:00", "thursday": "00:00-24:00", "friday": "00:00-24:00", "saturday": "00:00-24:00", "sunday": "00:00-24:00"}';

  // Generate contact info
  const contactInfo = data.contact_info || '{"phone": "", "email": ""}';

  // Generate location path
  const locationPath = data.location_path ||
    `${slugify(data.country || 'poland')}/${slugify(data.region)}/${slugify(data.location)}`;

  // Use current date for generation timestamp
  const currentDate = formatDate(new Date().toISOString());

  return `title: "${title}"
publishedDate: ${currentDate}
description_short: "${description}"
address_line: "${addressLine}"
coordinates:
  lat: ${lat}
  lon: ${lon}
work_hours: '${workHours}'
contact_info: '${contactInfo}'
maps_url: "https://maps.google.com/?q=${lat},${lon}"
rating: ${parseInt(data.rating) || 0}
locale: "${language}"
amenities:
    toilets: ${parseBoolean(data.toilets_available)}
    wifi: false
    fuel_station: ${parseBoolean(data.gas_station_available)}
    restaurant: ${parseBoolean(data.restaurant)}
    shop: false
    playground: false
    showers: ${parseBoolean(data.showers_available)}
    car_wash: ${parseBoolean(data.car_wash_available)}
    ev_charging: ${parseBoolean(data.ev_charging)}
    security: ${parseBoolean(data.security_personnel_on_site)}
    cctv: ${parseBoolean(data.cctv)}
    lighting: ${parseBoolean(data.lighting)}
    accommodation: ${parseBoolean(data.accommodation_available)}
    fenced_area: ${parseBoolean(data.fenced_area)}
featured_image: "https://placehold.co/600x400?text=${encodeURIComponent(data.rest_area_id || 'Rest+Area')}"
country_code: "${data.country_code || 'PL'}"
location_path: "${locationPath}"
highway_tag: "${data.road_class}${data.road_number}"
administrator: "${data.administrator || ''}"
mop_category: "${data.mop_category || ''}"
road_class: "${data.road_class || ''}"
road_number: "${data.road_number || ''}"
km_marker: "${data.km_marker || ''}"
travel_direction: "${data.travel_direction || ''}"
parking_spaces_cars: ${parseInt(data.parking_spaces_cars) || 0}
parking_spaces_trucks: ${parseInt(data.parking_spaces_trucks) || 0}
parking_spaces_buses: ${parseInt(data.parking_spaces_buses) || 0}
parking_spaces_dangerous: ${parseInt(data.adr_vehicle_spaces_count) || 0}
toilets_accessible: ${parseBoolean(data.toilets_accessible)}
ev_charger_details: "${data.ev_charger_details || ''}"
security_personnel_on_site: ${parseBoolean(data.security_personnel_on_site)}
last_verified_date: ${currentDate}
data_source_url: "${data.data_source_url || ''}"
internal_notes: "${data.internal_notes || ''}"`;
}

// Generate markdown content body with language support
function generateContent(data, language = 'en') {
  const restAreaName = data.rest_area_id || 'Rest Area';
  const region = data.region || 'Region';
  const location = data.location || 'Location';
  const roadClass = data.road_class || '';
  const roadNumber = data.road_number || '';
  const kmMarker = data.km_marker || '';
  const travelDirection = data.travel_direction || '';
  
  // Language-specific content
  const content = language === 'pl' ? {
    heading: `### ${restAreaName} - Usługi Autostradowe w ${region}`,
    locationText: `Położony na autostradzie ${roadClass}${roadNumber} przy kilometrze ${kmMarker} w regionie ${region}, ${location} oferuje podstawowe usługi dla podróżnych jadących w kierunku ${travelDirection}. Ten obiekt zapewnia wygodny punkt postojowy z różnymi udogodnieniami dla komfortowej podróży.`,
    facilitiesHeading: '#### Udogodnienia i Usługi',
    facilitiesText: 'Obiekt działa 24/7, co czyni go idealnym miejscem postoju o każdej porze. Obszar wypoczynku oferuje:',
    locationHeading: '#### Lokalizacja i Dostęp',
    locationText: `Położony przy kilometrze ${kmMarker} na autostradzie ${roadClass}${roadNumber}, obszar wypoczynku jest łatwo dostępny dla pojazdów jadących w kierunku ${travelDirection}.`,
    parkingText: 'Obiekt zapewnia miejsca parkingowe dla:',
    safetyHeading: '#### Bezpieczeństwo',
    safetyText: 'Obszar jest dobrze oświetlony i wyposażony w nowoczesne systemy bezpieczeństwa.',
    additionalHeading: '##### Dodatkowe Informacje',
    lastVerified: '**Ostatnia aktualizacja:**',
    dataSource: '**Źródło danych:**'
  } : {
    heading: `### ${restAreaName} Rest Area - Highway Services in ${region}`,
    locationText: `Located on the ${roadClass}${roadNumber} highway at kilometer marker ${kmMarker} in the ${region} region, ${location} offers essential services for travelers heading towards ${travelDirection}. This facility provides a convenient stopping point with various amenities to ensure a comfortable journey.`,
    facilitiesHeading: '#### Facilities & Services',
    facilitiesText: 'The facility operates 24/7, making it an ideal stop for travelers at any time. The rest area features:',
    locationHeading: '#### Location & Access',
    locationText: `Situated at kilometer marker ${kmMarker} on the ${roadClass}${roadNumber} highway, the rest area is easily accessible for vehicles traveling in the ${travelDirection} direction.`,
    parkingText: 'The facility provides parking for:',
    safetyHeading: '#### Safety & Security',
    safetyText: 'The area is well-lit and equipped with modern safety features.',
    additionalHeading: '##### Additional Information',
    lastVerified: '**Last Generated:**',
    dataSource: '**Data Source:**'
  };
  
  // Generate amenities list
  const amenities = [];
  if (parseBoolean(data.toilets_available)) amenities.push(language === 'pl' ? '- Czyste toalety' : '- Clean toilet facilities');
  if (parseBoolean(data.gas_station_available)) amenities.push(language === 'pl' ? '- Stacja paliw' : '- Fuel station');
  if (parseBoolean(data.restaurant_bistro_available)) amenities.push(language === 'pl' ? '- Restauracja/Bistro' : '- Restaurant/Bistro');
  if (parseBoolean(data.showers_available)) amenities.push(language === 'pl' ? '- Prysznice' : '- Showers');
  if (parseBoolean(data.car_wash_available)) amenities.push(language === 'pl' ? '- Myjnia samochodowa' : '- Car wash');
  if (parseBoolean(data.ev_charging_station)) amenities.push(language === 'pl' ? '- Stacja ładowania EV' : '- EV charging station');
  if (parseBoolean(data.wifi)) amenities.push(language === 'pl' ? '- Dostęp do Wi-Fi' : '- Wi-Fi access');
  if (parseBoolean(data.shop)) amenities.push(language === 'pl' ? '- Sklep' : '- Shop');
  if (parseBoolean(data.playground)) amenities.push(language === 'pl' ? '- Plac zabaw' : '- Playground');
  if (parseBoolean(data.accommodation_available)) amenities.push(language === 'pl' ? '- Noclegi' : '- Accommodation');
  
  // Generate parking info
  const parkingSpaces = [];
  if (data.parking_spaces_cars) parkingSpaces.push(language === 'pl' ? `- Samochody: ${data.parking_spaces_cars} miejsc` : `- Cars: ${data.parking_spaces_cars} spaces`);
  if (data.parking_spaces_trucks_tir) parkingSpaces.push(language === 'pl' ? `- Ciężarówki: ${data.parking_spaces_trucks_tir} miejsc` : `- Trucks: ${data.parking_spaces_trucks_tir} spaces`);
  if (data.parking_spaces_buses) parkingSpaces.push(language === 'pl' ? `- Autobusy: ${data.parking_spaces_buses} miejsc` : `- Buses: ${data.parking_spaces_buses} spaces`);
  
  // Generate safety features
  const safetyFeatures = [];
  if (parseBoolean(data.area_lighting)) safetyFeatures.push(language === 'pl' ? 'Oświetlenie obszaru zapewnia bezpieczeństwo.' : 'Area lighting provides enhanced security.');
  if (parseBoolean(data.cctv_video_surveillance)) safetyFeatures.push(language === 'pl' ? 'Monitoring CCTV zapewnia dodatkowe bezpieczeństwo.' : 'CCTV surveillance provides enhanced security.');
  if (parseBoolean(data.security_personnel_on_site)) safetyFeatures.push(language === 'pl' ? 'Personel ochrony jest dostępny na miejscu.' : 'Security personnel are available on-site.');
  if (parseBoolean(data.fenced_area)) safetyFeatures.push(language === 'pl' ? 'Obszar jest ogrodzony dla dodatkowego bezpieczeństwa.' : 'The area is fenced for added security.');
  
  // Use current date for generation timestamp
  const currentDate = new Date().toISOString().slice(0, 10);

  return `${content.heading}

${content.locationText}

${content.facilitiesHeading}

${content.facilitiesText}

${amenities.join('\n')}

${content.locationHeading}

${content.locationText}

${content.parkingText}

${parkingSpaces.join('\n')}

${content.safetyHeading}

${content.safetyText}

${safetyFeatures.join(' ')}

${language === 'pl' ? 'Obiekt utrzymuje wysokie standardy czystości i bezpieczeństwa dla wszystkich odwiedzających.' : 'The facility maintains high standards of cleanliness and safety for all visitors.'}

${content.additionalHeading}

- ${content.lastVerified} ${currentDate}
- ${content.dataSource} ${data.data_source_url ? `[Link](${data.data_source_url})` : 'N/A'}
`;
}

// Create backup of existing content
async function createBackup(language) {
  const sourceDir = CONFIG.outputDirs[language];
  const backupDir = path.join(CONFIG.backupDir, language, new Date().toISOString().slice(0, 19).replace(/:/g, '-'));
  
  try {
    await fs.access(sourceDir);
    await fs.mkdir(backupDir, { recursive: true });
    
    const files = await fs.readdir(sourceDir);
    const mdFiles = files.filter(file => file.endsWith('.md'));
    
    for (const file of mdFiles) {
      const sourcePath = path.join(sourceDir, file);
      const backupPath = path.join(backupDir, file);
      await fs.copyFile(sourcePath, backupPath);
    }
    
    console.log(`📦 Created backup for ${language}: ${backupDir}`);
    return backupDir;
  } catch (error) {
    console.log(`ℹ️  No existing content to backup for ${language}`);
    return null;
  }
}

// Generate markdown files from JSON data
async function generateFromJson(inputFile, language) {
  try {
    console.log(`\n🔄 Processing ${language.toUpperCase()} content from ${inputFile}...`);
    
    // Read JSON data
    const jsonContent = await fs.readFile(inputFile, 'utf-8');
    const data = JSON.parse(jsonContent);
    
    if (!Array.isArray(data) || data.length === 0) {
      console.warn(`⚠️  No data found in ${inputFile}`);
      return { generated: 0, errors: [] };
    }
    
    // Create backup
    await createBackup(language);
    
    // Ensure output directory exists
    await fs.mkdir(CONFIG.outputDirs[language], { recursive: true });
    
    const generatedSlugs = {};
    const errors = [];
    let generated = 0;
    
    for (const [index, row] of data.entries()) {
      try {
        // Generate unique slug
        let baseSlug = slugify(`${row.rest_area_id}-${row.road_class}${row.road_number}`);
        if (!baseSlug) {
          baseSlug = slugify(`rest-area-${index + 1}`);
        }
        
        let currentSlug = baseSlug;
        let counter = 1;
        
        while (generatedSlugs[currentSlug]) {
          counter++;
          currentSlug = `${baseSlug}-${counter}`;
        }
        generatedSlugs[currentSlug] = true;
        
        // Generate content
        const frontmatter = generateFrontmatter(row, language);
        const content = generateContent(row, language);
        
        const filename = `mop-${currentSlug}.md`;
        const outputPath = path.join(CONFIG.outputDirs[language], filename);
        
        const fileContent = `---\n${frontmatter}\n---\n\n${content.trim()}\n`;
        
        await fs.writeFile(outputPath, fileContent, 'utf-8');
        generated++;
        
        if (generated % 10 === 0) {
          console.log(`   Generated ${generated}/${data.length} files...`);
        }
        
      } catch (error) {
        const errorMsg = `Row ${index + 1}: ${error.message}`;
        errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }
    
    console.log(`✅ Generated ${generated} markdown files for ${language}`);
    return { generated, errors };
    
  } catch (error) {
    console.error(`💥 Failed to process ${language}:`, error.message);
    return { generated: 0, errors: [error.message] };
  }
}

// Discover available language files
async function discoverLanguageFiles(requestedLanguages = null) {
  const availableLanguages = [];
  const supportedLanguages = ['en', 'pl']; // Define supported languages

  // If specific languages are requested, use those; otherwise check all supported
  const languagesToCheck = requestedLanguages || supportedLanguages;

  console.log('🔍 Discovering available language files...');

  for (const language of languagesToCheck) {
    // Validate that the requested language is supported
    if (!supportedLanguages.includes(language)) {
      console.warn(`⚠️  Unsupported language: ${language}. Supported languages: ${supportedLanguages.join(', ')}`);
      continue;
    }

    const inputFile = path.join(CONFIG.inputDir, `rest-areas-${language}.json`);

    try {
      await fs.access(inputFile);
      availableLanguages.push(language);
      console.log(`✅ Found: rest-areas-${language}.json`);
    } catch (error) {
      if (requestedLanguages) {
        console.warn(`⚠️  Requested file not found: rest-areas-${language}.json`);
      } else {
        console.log(`ℹ️  Skipped: rest-areas-${language}.json (not found)`);
      }
    }
  }

  if (availableLanguages.length === 0) {
    const expectedFiles = languagesToCheck.map(lang => `rest-areas-${lang}.json`).join(', ');
    throw new Error(`No language files found in the input directory. Expected files: ${expectedFiles}`);
  }

  console.log(`📋 Processing ${availableLanguages.length} language(s): ${availableLanguages.join(', ')}`);
  return availableLanguages;
}

// Main function
async function main(requestedLanguages = null) {
  try {
    console.log('🚀 Starting markdown generation from JSON...');

    if (requestedLanguages) {
      console.log(`🎯 Requested languages: ${requestedLanguages.join(', ')}`);
    }

    const results = {
      timestamp: new Date().toISOString(),
      languages: {},
      summary: { totalGenerated: 0, totalErrors: 0 }
    };

    // Discover which language files are available
    const availableLanguages = await discoverLanguageFiles(requestedLanguages);

    // Process each available language
    for (const language of availableLanguages) {
      const inputFile = path.join(CONFIG.inputDir, `rest-areas-${language}.json`);

      try {
        const result = await generateFromJson(inputFile, language);
        results.languages[language] = result;
        results.summary.totalGenerated += result.generated;
        results.summary.totalErrors += result.errors.length;
      } catch (error) {
        console.error(`❌ Failed to process ${language}:`, error.message);
        results.languages[language] = { generated: 0, errors: [error.message] };
        results.summary.totalErrors++;
      }
    }
    
    // Save generation summary
    const summaryFile = path.join(CONFIG.inputDir, 'generation-summary.json');
    await fs.writeFile(summaryFile, JSON.stringify(results, null, 2), 'utf-8');
    
    console.log('\n📋 Generation Summary:');
    console.log(`   Languages processed: ${Object.keys(results.languages).length}`);
    console.log(`   Total files generated: ${results.summary.totalGenerated}`);
    console.log(`   Total errors: ${results.summary.totalErrors}`);

    Object.entries(results.languages).forEach(([lang, result]) => {
      console.log(`   ${lang.toUpperCase()}: ${result.generated} files, ${result.errors.length} errors`);
    });

    // Only exit with error if there were actual processing errors, not missing files
    if (results.summary.totalErrors > 0) {
      console.error('\n❌ Generation completed with errors.');
      console.error('Please check the error messages above and fix any issues.');
      process.exit(1);
    } else if (results.summary.totalGenerated === 0) {
      console.warn('\n⚠️  No files were generated. Check if input files contain valid data.');
      process.exit(1);
    } else {
      console.log('\n✅ Generation completed successfully!');
      console.log(`🎉 Generated ${results.summary.totalGenerated} markdown files from available language data.`);
    }
    
  } catch (error) {
    console.error('💥 Generation failed:', error.message);
    process.exit(1);
  }
}

// Parse command line arguments
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  let requestedLanguages = null;

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--languages' || arg === '-l') {
      if (i + 1 < args.length) {
        requestedLanguages = args[i + 1].split(',').map(lang => lang.trim());
        i++; // Skip the next argument as it's the value
      } else {
        console.error('❌ --languages option requires a value (e.g., --languages en,pl)');
        process.exit(1);
      }
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
📝 Markdown Generation Script

Usage: node scripts/generate-from-json.js [options]

Options:
  --languages, -l <langs>  Comma-separated list of languages to process (e.g., en,pl)
  --help, -h              Show this help message

Examples:
  node scripts/generate-from-json.js                    # Process all available languages
  node scripts/generate-from-json.js --languages en    # Process only English
  node scripts/generate-from-json.js -l en,pl          # Process English and Polish

Supported languages: en, pl
`);
      process.exit(0);
    } else {
      console.warn(`⚠️  Unknown argument: ${arg}. Use --help for usage information.`);
    }
  }

  return { requestedLanguages };
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const { requestedLanguages } = parseCommandLineArgs();
  main(requestedLanguages);
}

export { generateFromJson, main, CONFIG };
