---
/**
 * Content Ad Component
 * Optimized ad placement within content areas
 */

import AdUnit from '../AdUnit.astro';

export interface Props {
  adSlot?: string;
  format?: 'rectangle' | 'horizontal' | 'auto';
  showLabel?: boolean;
  className?: string;
  position?: 'top' | 'middle' | 'bottom';
}

const {
  adSlot = '3456789012', // Default placeholder - replace with actual ad slot
  format = 'rectangle',
  showLabel = true,
  className = '',
  position = 'middle'
} = Astro.props;

// Position-specific styling
const positionClasses = {
  top: 'content-ad-top',
  middle: 'content-ad-middle',
  bottom: 'content-ad-bottom'
};
---

<AdUnit
  adSlot={adSlot}
  format={format}
  placement="content"
  responsive={true}
  lazy={true}
  className={`content-ad ${positionClasses[position]} ${className}`}
  label={showLabel ? "Advertisement" : undefined}
/>

<style>
  .content-ad {
    /* Content ad base styling */
    margin: 2rem auto;
    padding: 1.5rem;
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    text-align: center;
  }
  
  .dark .content-ad {
    background: #111827;
    border-color: #374151;
  }
  
  .content-ad-top {
    margin-top: 1rem;
    margin-bottom: 2rem;
  }
  
  .content-ad-middle {
    margin: 3rem auto;
  }
  
  .content-ad-bottom {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }
  
  @media (max-width: 768px) {
    .content-ad {
      margin: 1.5rem auto;
      padding: 1rem;
    }
    
    .content-ad-middle {
      margin: 2rem auto;
    }
  }
  
  /* Blend with content */
  .content-ad::before {
    content: '';
    display: block;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #d1d5db, transparent);
    margin: 0 auto 1rem;
  }
  
  .dark .content-ad::before {
    background: linear-gradient(90deg, transparent, #4b5563, transparent);
  }
</style>
