#!/usr/bin/env node

/**
 * Backup existing Polish files and regenerate them with improved translations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const polishDir = path.join(__dirname, '../src/content/rest-areas/pl');
const backupDir = path.join(__dirname, '../src/content/rest-areas/pl-backup-' + new Date().toISOString().slice(0, 10));

async function createBackup() {
  console.log('📦 Creating backup of existing Polish files...');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const files = fs.readdirSync(polishDir).filter(file => file.endsWith('.md'));
  
  for (const file of files) {
    const sourcePath = path.join(polishDir, file);
    const backupPath = path.join(backupDir, file);
    fs.copyFileSync(sourcePath, backupPath);
  }
  
  console.log(`✅ Backed up ${files.length} files to: ${backupDir}`);
  return files.length;
}

async function removeExistingPolishFiles() {
  console.log('🗑️  Removing existing Polish files...');
  
  const files = fs.readdirSync(polishDir).filter(file => file.endsWith('.md'));
  
  for (const file of files) {
    const filePath = path.join(polishDir, file);
    fs.unlinkSync(filePath);
  }
  
  console.log(`✅ Removed ${files.length} existing Polish files`);
  return files.length;
}

async function regeneratePolishFiles() {
  console.log('🔄 Regenerating Polish files with improved translations...');
  
  // Import and run the main translation script
  const { execSync } = await import('child_process');
  
  try {
    const output = execSync('node create-pl-translations.js', { 
      cwd: __dirname,
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log(output);
    return true;
  } catch (error) {
    console.error('❌ Error regenerating files:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting backup and regeneration process...\n');
  
  try {
    // Step 1: Create backup
    const backedUpCount = await createBackup();
    
    // Step 2: Remove existing files
    const removedCount = await removeExistingPolishFiles();
    
    // Step 3: Regenerate with improved translations
    const success = await regeneratePolishFiles();
    
    if (success) {
      console.log('\n🎉 Process completed successfully!');
      console.log(`📊 Summary:`);
      console.log(`   - Backed up: ${backedUpCount} files`);
      console.log(`   - Removed: ${removedCount} files`);
      console.log(`   - Regenerated with improved translations`);
      console.log(`\n💡 Backup location: ${backupDir}`);
    } else {
      console.log('\n❌ Process failed during regeneration');
      console.log('💡 Original files are safely backed up');
    }
    
  } catch (error) {
    console.error('❌ Process failed:', error.message);
    console.log('💡 Check backup directory for original files');
  }
}

main();
