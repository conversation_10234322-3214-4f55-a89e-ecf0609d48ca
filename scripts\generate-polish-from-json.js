#!/usr/bin/env node

/**
 * Polish Content Generation Script
 * 
 * This script generates Polish markdown files by combining translated frontmatter
 * from English files with manually translated content from Polish JSON files.
 * This approach completely eliminates language mixing issues.
 * 
 * Usage: node generate-polish-from-json.js [filename]
 * If no filename is provided, processes all available Polish JSON files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directory paths
const englishMdDir = path.join(__dirname, '../src/content/rest-areas/en');
const polishJsonDir = path.join(__dirname, '../src/content/rest-areas/translation-json/pl');
const polishMdDir = path.join(__dirname, '../src/content/rest-areas/pl');
const consolidatedPolishJsonFile = path.join(polishJsonDir, 'all-content-for-translation.json');

// Translation mappings for frontmatter fields
const frontmatterTranslations = {
  // Basic terms
  'Rest Area': 'MOP',
  'Highway Services': 'Usługi Drogowe',
  'MOP commercial function': 'MOP funkcja komercyjna',
  'Private': 'Prywatny',
  'Public': 'Publiczny',
  'State': 'Państwowy',
  'Service Area': 'Miejsce Obsługi Podróżnych',
  'Rest Stop': 'Miejsce Odpoczynku',
  'Private Parking': 'Parking Prywatny',
  
  // Regions - convert to proper Polish case for descriptions
  'Małopolskie': 'małopolskim',
  'Mazowieckie': 'mazowieckim',
  'Śląskie': 'śląskim',
  'Wielkopolskie': 'wielkopolskim',
  'Dolnośląskie': 'dolnośląskim',
  'Lubelskie': 'lubelskim',
  'Łódzkie': 'łódzkim',
  'Pomorskie': 'pomorskim',
  'Warmińsko-Mazurskie': 'warmińsko-mazurskim',
  'Zachodniopomorskie': 'zachodniopomorskim',
  'Kujawsko-Pomorskie': 'kujawsko-pomorskim',
  'Lubuskie': 'lubuskim',
  'Opolskie': 'opolskim',
  'Podkarpackie': 'podkarpackim',
  'Świętokrzyskie': 'świętokrzyskim',
  'Podlaskie': 'podlaskim',
  
  // Common phrases in descriptions
  'on road in Poland.': 'na drodze w Polsce.',
  'road in Poland.': 'drodze w Polsce.',
};

/**
 * Escape regex special characters
 * @param {string} string - String to escape
 * @returns {string} - Escaped string
 */
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Translate text using the frontmatter translation mappings
 * @param {string} text - Text to translate
 * @returns {string} - Translated text
 */
function translateFrontmatterText(text) {
  let translated = text;
  
  // Apply translations (sorted by length, longest first to avoid partial matches)
  const sortedTranslations = Object.entries(frontmatterTranslations)
    .sort(([a], [b]) => b.length - a.length);
  
  sortedTranslations.forEach(([english, polish]) => {
    const escapedEnglish = escapeRegExp(english);
    const regex = new RegExp(escapedEnglish, 'gi');
    translated = translated.replace(regex, polish);
  });
  
  return translated;
}

/**
 * Parse markdown file and extract frontmatter
 * @param {string} fileContent - Raw file content
 * @returns {string} - Frontmatter content
 */
function extractFrontmatter(fileContent) {
  const normalizedContent = fileContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
  const frontmatterRegex = /^---\n([\s\S]*?)\n---\n/;
  const match = normalizedContent.match(frontmatterRegex);
  
  if (!match) {
    throw new Error('Invalid markdown file format - missing frontmatter');
  }
  
  return match[1].trim();
}

/**
 * Create Polish frontmatter by combining English structure with Polish translations
 * @param {string} englishFrontmatterText - Original English frontmatter
 * @param {Object} polishTranslations - Polish translations from JSON
 * @returns {string} - Complete Polish frontmatter
 */
function createPolishFrontmatter(englishFrontmatterText, polishTranslations) {
  let polishFrontmatter = englishFrontmatterText;

  // Replace translatable fields with Polish versions
  if (polishTranslations.title) {
    polishFrontmatter = polishFrontmatter.replace(
      /title: "([^"]+)"/g,
      `title: "${polishTranslations.title}"`
    );
  }

  if (polishTranslations.description_short) {
    polishFrontmatter = polishFrontmatter.replace(
      /description_short: "([^"]+)"/g,
      `description_short: "${polishTranslations.description_short}"`
    );
  }

  if (polishTranslations.administrator) {
    polishFrontmatter = polishFrontmatter.replace(
      /administrator: "([^"]+)"/g,
      `administrator: "${polishTranslations.administrator}"`
    );
  }

  if (polishTranslations.mop_category) {
    polishFrontmatter = polishFrontmatter.replace(
      /mop_category: "([^"]+)"/g,
      `mop_category: "${polishTranslations.mop_category}"`
    );
  }

  // Update featured_image text parameter if it exists
  polishFrontmatter = polishFrontmatter.replace(/featured_image: "([^"]+)"/g, (_, imageUrl) => {
    if (imageUrl.includes('text=') && polishTranslations.title) {
      const updatedUrl = imageUrl.replace(
        /text=([^&"]+)/g,
        (_, text) => `text=${encodeURIComponent(polishTranslations.title)}`
      );
      return `featured_image: "${updatedUrl}"`;
    }
    return `featured_image: "${imageUrl}"`;
  });

  // Add locale field
  polishFrontmatter += '\nlocale: "pl"';

  return polishFrontmatter;
}

/**
 * Generate Polish markdown file from consolidated JSON content and English frontmatter
 * @param {string} markdownFilename - Name of the markdown file (e.g., "mop-aleksandrowice-a4.md")
 * @param {Object} polishData - Polish translation data from consolidated JSON
 * @returns {boolean} - Success status
 */
function generatePolishMarkdownFromConsolidated(markdownFilename, polishData) {
  const englishMdPath = path.join(englishMdDir, markdownFilename);
  const polishMdPath = path.join(polishMdDir, markdownFilename);

  // Check if corresponding English markdown file exists
  if (!fs.existsSync(englishMdPath)) {
    console.error(`❌ Corresponding English markdown file not found: ${markdownFilename}`);
    return false;
  }

  // Check if Polish markdown file already exists
  if (fs.existsSync(polishMdPath)) {
    console.log(`⚠️  Polish markdown file already exists: ${markdownFilename}`);
    return false;
  }

  try {
    // Validate Polish data structure
    if (!polishData.content || typeof polishData.content !== 'string') {
      throw new Error('Invalid Polish data structure - missing or invalid content field');
    }

    if (!polishData.frontmatter || typeof polishData.frontmatter !== 'object') {
      throw new Error('Invalid Polish data structure - missing or invalid frontmatter field');
    }

    // Read English markdown file for complete frontmatter structure
    const englishMdContent = fs.readFileSync(englishMdPath, 'utf-8');
    const englishFrontmatter = extractFrontmatter(englishMdContent);

    // Create Polish frontmatter by combining English structure with Polish translations
    const polishFrontmatter = createPolishFrontmatter(englishFrontmatter, polishData.frontmatter);

    // Combine frontmatter and content
    const polishMarkdown = `---\n${polishFrontmatter}\n---\n${polishData.content}`;

    // Ensure output directory exists
    if (!fs.existsSync(polishMdDir)) {
      fs.mkdirSync(polishMdDir, { recursive: true });
    }

    // Write Polish markdown file
    fs.writeFileSync(polishMdPath, polishMarkdown, 'utf-8');

    console.log(`✅ Generated Polish markdown: ${markdownFilename}`);
    console.log(`   Content: ${polishData.content.split('\n').length} lines, ${polishData.content.length} characters`);

    return true;

  } catch (error) {
    console.error(`❌ Error processing ${markdownFilename}:`, error.message);
    return false;
  }
}

/**
 * Process consolidated Polish JSON file and generate all markdown files
 * @returns {Object} - Processing results
 */
function processConsolidatedPolishJson() {
  // Check if consolidated Polish JSON file exists
  if (!fs.existsSync(consolidatedPolishJsonFile)) {
    throw new Error(`Consolidated Polish JSON file not found: ${consolidatedPolishJsonFile}`);
  }

  try {
    // Read and parse consolidated Polish JSON
    const polishJsonContent = fs.readFileSync(consolidatedPolishJsonFile, 'utf-8');
    const consolidatedPolishData = JSON.parse(polishJsonContent);

    // Validate consolidated structure
    if (typeof consolidatedPolishData !== 'object' || consolidatedPolishData === null) {
      throw new Error('Invalid consolidated Polish JSON structure - expected object');
    }

    const filenames = Object.keys(consolidatedPolishData);

    if (filenames.length === 0) {
      throw new Error('Consolidated Polish JSON file is empty');
    }

    console.log(`🔄 Processing ${filenames.length} files from consolidated Polish JSON...\n`);

    let processed = 0;
    let generated = 0;
    let skipped = 0;
    let errors = 0;

    for (const filename of filenames) {
      processed++;

      // Show progress for large batches
      if (processed % 50 === 0) {
        console.log(`📊 Progress: ${processed}/${filenames.length} files processed...`);
      }

      const polishData = consolidatedPolishData[filename];
      const result = generatePolishMarkdownFromConsolidated(filename, polishData);

      if (result === true) {
        generated++;
      } else if (result === false && fs.existsSync(path.join(polishMdDir, filename))) {
        skipped++;
      } else {
        errors++;
      }
    }

    return {
      processed,
      generated,
      skipped,
      errors,
      consolidatedFile: consolidatedPolishJsonFile
    };

  } catch (error) {
    throw new Error(`Error processing consolidated Polish JSON: ${error.message}`);
  }
}

/**
 * Get all Polish JSON files (legacy support for individual files)
 * @returns {string[]} - Array of Polish JSON filenames
 */
function getPolishJsonFiles() {
  if (!fs.existsSync(polishJsonDir)) {
    throw new Error(`Polish JSON directory not found: ${polishJsonDir}`);
  }

  return fs.readdirSync(polishJsonDir)
    .filter(file => file.endsWith('.json') && file !== 'all-content-for-translation.json')
    .sort();
}

/**
 * Main execution function
 */
function main() {
  const args = process.argv.slice(2);

  console.log('🇵🇱 Polish Content Generation from Consolidated JSON');
  console.log('====================================================\n');

  try {
    if (args.length > 0) {
      // Process specific file from consolidated JSON
      const markdownFilename = args[0].endsWith('.md') ? args[0] : args[0] + '.md';
      console.log(`🔍 Processing specific file from consolidated JSON: ${markdownFilename}\n`);

      // Check if consolidated Polish JSON file exists
      if (!fs.existsSync(consolidatedPolishJsonFile)) {
        console.error(`❌ Consolidated Polish JSON file not found: ${path.basename(consolidatedPolishJsonFile)}`);
        console.log(`💡 Expected location: ${consolidatedPolishJsonFile}`);
        console.log('💡 Create consolidated Polish JSON file first');
        return;
      }

      try {
        // Read consolidated Polish JSON
        const polishJsonContent = fs.readFileSync(consolidatedPolishJsonFile, 'utf-8');
        const consolidatedPolishData = JSON.parse(polishJsonContent);

        // Check if specific file exists in consolidated data
        if (!consolidatedPolishData[markdownFilename]) {
          console.error(`❌ File not found in consolidated Polish JSON: ${markdownFilename}`);
          console.log(`💡 Available files: ${Object.keys(consolidatedPolishData).length} entries`);
          return;
        }

        const polishData = consolidatedPolishData[markdownFilename];
        const success = generatePolishMarkdownFromConsolidated(markdownFilename, polishData);

        if (success) {
          console.log('\n🎉 Polish markdown generation completed successfully!');
          console.log(`📁 Output directory: ${polishMdDir}`);
        } else {
          console.log('\n❌ Polish markdown generation failed');
        }

      } catch (error) {
        console.error(`❌ Error reading consolidated Polish JSON: ${error.message}`);
      }

    } else {
      // Process all files from consolidated JSON
      console.log('🔍 Looking for consolidated Polish JSON file...\n');

      // Check if consolidated Polish JSON file exists
      if (!fs.existsSync(consolidatedPolishJsonFile)) {
        console.log('📭 Consolidated Polish JSON file not found.');
        console.log(`💡 Expected location: ${consolidatedPolishJsonFile}`);
        console.log('💡 Create Polish translations in consolidated format first');

        // Check for legacy individual JSON files
        const legacyFiles = getPolishJsonFiles();
        if (legacyFiles.length > 0) {
          console.log(`\n⚠️  Found ${legacyFiles.length} legacy individual JSON files.`);
          console.log('💡 Consider consolidating them into a single file for better workflow.');
        }
        return;
      }

      console.log(`📦 Found consolidated Polish JSON file: ${path.basename(consolidatedPolishJsonFile)}`);

      // Process consolidated JSON file
      const results = processConsolidatedPolishJson();

      // Final summary
      console.log('\n📊 Generation Summary:');
      console.log('======================');
      console.log(`📝 Total files processed: ${results.processed}`);
      console.log(`✅ Successfully generated: ${results.generated}`);
      console.log(`⚠️  Skipped (already exist): ${results.skipped}`);
      console.log(`❌ Errors: ${results.errors}`);
      console.log(`📦 Source: ${path.basename(results.consolidatedFile)}`);
      console.log(`📁 Output directory: ${polishMdDir}`);

      if (results.generated > 0) {
        console.log('\n🎉 Polish markdown generation completed successfully!');
        console.log('\n📋 Next Steps:');
        console.log('1. Review the generated Polish markdown files');
        console.log('2. Test the multilingual site functionality');
        console.log('3. Verify that language mixing issues are resolved');
      }
    }

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
