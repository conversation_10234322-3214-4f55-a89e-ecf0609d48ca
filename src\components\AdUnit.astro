---
/**
 * AdSense Ad Unit Component
 * Reusable component for placing AdSense ads throughout the site
 */

export interface Props {
  adSlot: string;
  format?: 'auto' | 'rectangle' | 'vertical' | 'horizontal' | 'fluid';
  size?: string;
  responsive?: boolean;
  lazy?: boolean;
  className?: string;
  style?: string;
  label?: string;
  placement?: 'header' | 'sidebar' | 'content' | 'footer' | 'inline';
}

const {
  adSlot,
  format = 'auto',
  size,
  responsive = true,
  lazy = true,
  className = '',
  style = '',
  label,
  placement = 'content'
} = Astro.props;

// Get publisher ID from environment
const publisherId = import.meta.env.PUBLIC_ADSENSE_PUBLISHER_ID;

// Only render if we have a publisher ID and ad slot
const shouldRender = publisherId && adSlot;

// Generate unique ID for this ad unit
const adId = `ad-${adSlot}-${Math.random().toString(36).substr(2, 9)}`;

// Placement-specific styling
const placementStyles = {
  header: 'my-4',
  sidebar: 'my-6',
  content: 'my-8',
  footer: 'my-4',
  inline: 'my-4'
};

// Responsive size configurations
const responsiveSizes = {
  auto: { width: '100%', height: 'auto' },
  rectangle: { width: '336px', height: '280px' },
  vertical: { width: '160px', height: '600px' },
  horizontal: { width: '728px', height: '90px' },
  fluid: { width: '100%', height: 'auto' }
};

const adSize = size || responsiveSizes[format] || responsiveSizes.auto;
---

{shouldRender && (
  <div 
    class={`ad-container ${placementStyles[placement]} ${className}`}
    data-ad-placement={placement}
    style={style}
  >
    {label && (
      <div class="ad-label text-xs text-secondary-500 dark:text-secondary-400 mb-2 text-center">
        {label}
      </div>
    )}
    
    <div 
      id={adId}
      class="ad-unit-wrapper flex justify-center"
    >
      <ins 
        class={`adsbygoogle ${lazy ? 'lazy-ad' : ''}`}
        style={`display: block; width: ${typeof adSize.width === 'string' ? adSize.width : adSize.width + 'px'}; height: ${typeof adSize.height === 'string' ? adSize.height : adSize.height + 'px'};`}
        data-ad-client={publisherId}
        data-ad-slot={adSlot}
        data-ad-format={format}
        {responsive && { 'data-full-width-responsive': 'true' }}
        {lazy && { 'data-lazy': 'true' }}
        data-ad-placement={placement}
      ></ins>
    </div>
  </div>

  <!-- Initialize ad unit if not lazy loading -->
  {!lazy && (
    <script is:inline define:vars={{ adId }}>
      document.addEventListener('DOMContentLoaded', function() {
        if (window.adSenseManager && window.adSenseManager.consentGiven) {
          try {
            (adsbygoogle = window.adsbygoogle || []).push({});
          } catch (error) {
            console.warn('AdSense: Error initializing ad unit:', error);
          }
        }
      });
    </script>
  )}
)}

<style>
  .ad-container {
    /* Prevent layout shift */
    min-height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .ad-unit-wrapper {
    /* Container for the ad unit */
    max-width: 100%;
    overflow: hidden;
  }
  
  .adsbygoogle {
    /* Ensure ads don't break layout */
    max-width: 100%;
    margin: 0 auto;
  }
  
  .lazy-ad {
    /* Placeholder for lazy-loaded ads */
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 25%, transparent 50%, #f0f0f0 50%, #f0f0f0 75%, transparent 75%, transparent);
    background-size: 20px 20px;
    animation: loading 1.5s infinite linear;
    min-height: 100px;
  }
  
  .dark .lazy-ad {
    background: linear-gradient(90deg, #374151 25%, transparent 25%, transparent 50%, #374151 50%, #374151 75%, transparent 75%, transparent);
    background-size: 20px 20px;
  }
  
  @keyframes loading {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
  }
  
  /* Hide ads when consent not given */
  .no-ad-consent .adsbygoogle {
    display: none !important;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .ad-container[data-ad-placement="sidebar"] {
      margin: 1rem 0;
    }
    
    .ad-container[data-ad-placement="header"] {
      margin: 0.5rem 0;
    }
  }
  
  /* Accessibility */
  .ad-label {
    font-family: system-ui, -apple-system, sans-serif;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
  
  /* Performance optimization */
  .adsbygoogle {
    /* Improve rendering performance */
    contain: layout style paint;
  }
</style>
