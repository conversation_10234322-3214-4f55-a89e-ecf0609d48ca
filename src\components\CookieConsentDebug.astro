---
/**
 * <PERSON><PERSON> Consent Debug Component
 * Helps troubleshoot cookie consent banner visibility issues
 */

export interface Props {
  showDebugInfo?: boolean;
}

const {
  showDebugInfo = !import.meta.env.PROD
} = Astro.props;

// Environment variables
const enableCookieConsent = import.meta.env.PUBLIC_ENABLE_COOKIE_CONSENT;
const enableDevAnalytics = import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS;
const enableAnalytics = import.meta.env.PUBLIC_ENABLE_ANALYTICS;
const isProduction = import.meta.env.PROD;

// Computed values
const shouldShowConsent = enableCookieConsent === 'true' && (isProduction || enableDevAnalytics === 'true');
---

{showDebugInfo && (
  <div 
    id="cookie-consent-debug" 
    class="fixed top-4 right-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded-lg p-4 text-sm z-[9999] max-w-sm"
    style="font-family: monospace;"
  >
    <h4 class="font-bold text-yellow-800 dark:text-yellow-200 mb-2">
      🍪 Cookie Consent Debug
    </h4>
    
    <div class="space-y-1 text-yellow-700 dark:text-yellow-300">
      <div>
        <strong>Environment:</strong>
      </div>
      <div class="ml-2">
        • PROD: <span class={isProduction ? 'text-green-600' : 'text-red-600'}>{isProduction ? 'true' : 'false'}</span>
      </div>
      <div class="ml-2">
        • ENABLE_COOKIE_CONSENT: <span class={enableCookieConsent === 'true' ? 'text-green-600' : 'text-red-600'}>{enableCookieConsent}</span>
      </div>
      <div class="ml-2">
        • ENABLE_DEV_ANALYTICS: <span class={enableDevAnalytics === 'true' ? 'text-green-600' : 'text-red-600'}>{enableDevAnalytics}</span>
      </div>
      <div class="ml-2">
        • ENABLE_ANALYTICS: <span class={enableAnalytics === 'true' ? 'text-green-600' : 'text-red-600'}>{enableAnalytics}</span>
      </div>
      
      <div class="mt-2">
        <strong>Computed:</strong>
      </div>
      <div class="ml-2">
        • Should Show: <span class={shouldShowConsent ? 'text-green-600' : 'text-red-600'}>{shouldShowConsent ? 'YES' : 'NO'}</span>
      </div>
      
      <div class="mt-2">
        <strong>Banner Status:</strong>
      </div>
      <div class="ml-2" id="banner-status">
        • Checking...
      </div>
      
      <div class="mt-2">
        <strong>LocalStorage:</strong>
      </div>
      <div class="ml-2" id="storage-status">
        • Checking...
      </div>
    </div>
    
    <div class="mt-3 space-y-1">
      <button 
        onclick="clearCookieConsent()" 
        class="w-full px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
      >
        Clear Consent (Force Show)
      </button>
      <button 
        onclick="toggleDebugInfo()" 
        class="w-full px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
      >
        Hide Debug Info
      </button>
    </div>
  </div>

  <script is:inline>
    // Debug functions
    function clearCookieConsent() {
      localStorage.removeItem('cookie-consent');
      localStorage.removeItem('cookie-consent-date');
      console.log('🍪 Cookie consent cleared - banner should appear');
      location.reload();
    }
    
    function toggleDebugInfo() {
      const debugPanel = document.getElementById('cookie-consent-debug');
      if (debugPanel) {
        debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
      }
    }
    
    // Check banner and storage status
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        // Check if banner exists in DOM
        const banner = document.getElementById('cookie-consent-banner');
        const bannerStatus = document.getElementById('banner-status');
        if (bannerStatus) {
          if (banner) {
            const isVisible = banner.offsetParent !== null;
            bannerStatus.innerHTML = `• Banner: <span class="${isVisible ? 'text-green-600' : 'text-red-600'}">${isVisible ? 'VISIBLE' : 'HIDDEN'}</span>`;
          } else {
            bannerStatus.innerHTML = '• Banner: <span class="text-red-600">NOT FOUND</span>';
          }
        }
        
        // Check localStorage
        const storageStatus = document.getElementById('storage-status');
        if (storageStatus) {
          const consent = localStorage.getItem('cookie-consent');
          const consentDate = localStorage.getItem('cookie-consent-date');
          if (consent) {
            storageStatus.innerHTML = `• Consent: <span class="text-blue-600">SAVED</span> (${consentDate ? new Date(consentDate).toLocaleDateString() : 'no date'})`;
          } else {
            storageStatus.innerHTML = '• Consent: <span class="text-gray-600">NONE</span>';
          }
        }
        
        // Check Alpine.js
        if (typeof Alpine === 'undefined') {
          console.warn('🍪 Alpine.js not found - cookie consent banner may not work');
        }
        
        // Check for Alpine.js errors
        window.addEventListener('alpine:init', function() {
          console.log('🍪 Alpine.js initialized successfully');
        });
        
      }, 500);
    });
    
    // Global debug function
    window.debugCookieConsent = function() {
      console.log('🍪 Cookie Consent Debug Info:');
      console.log('Banner element:', document.getElementById('cookie-consent-banner'));
      console.log('Saved consent:', localStorage.getItem('cookie-consent'));
      console.log('Consent date:', localStorage.getItem('cookie-consent-date'));
      console.log('Alpine.js available:', typeof Alpine !== 'undefined');
      
      // Try to access Alpine data
      const banner = document.getElementById('cookie-consent-banner');
      if (banner && banner._x_dataStack) {
        console.log('Alpine data:', banner._x_dataStack[0]);
      }
    };
  </script>
)}

<!-- Always available debug styles -->
<style>
  /* Ensure debug panel is always on top */
  #cookie-consent-debug {
    z-index: 99999 !important;
  }
  
  /* Ensure cookie banner has proper z-index */
  #cookie-consent-banner {
    z-index: 50 !important;
  }
</style>
