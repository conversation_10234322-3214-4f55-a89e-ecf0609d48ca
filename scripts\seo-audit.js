#!/usr/bin/env node

/**
 * SEO Audit Script for stops24.com
 * 
 * This script performs automated SEO checks on the built website
 * Run with: node scripts/seo-audit.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { JSDOM } from 'jsdom';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const distDir = path.join(__dirname, '..', 'dist');

// SEO audit configuration
const SEO_RULES = {
  title: {
    minLength: 30,
    maxLength: 60,
    required: true
  },
  description: {
    minLength: 120,
    maxLength: 160,
    required: true
  },
  headings: {
    requireH1: true,
    maxH1Count: 1,
    logicalHierarchy: true
  },
  images: {
    requireAlt: true,
    maxAltLength: 125
  },
  links: {
    requireTitle: false,
    checkInternal: true
  }
};

class SEOAuditor {
  constructor() {
    this.results = {
      totalPages: 0,
      passedPages: 0,
      failedPages: 0,
      warnings: [],
      errors: [],
      summary: {}
    };
  }

  async auditDirectory(dirPath = distDir) {
    console.log('🔍 Starting SEO Audit...\n');
    
    const htmlFiles = this.findHtmlFiles(dirPath);
    this.results.totalPages = htmlFiles.length;
    
    console.log(`Found ${htmlFiles.length} HTML files to audit\n`);
    
    for (const filePath of htmlFiles) {
      await this.auditFile(filePath);
    }
    
    this.generateReport();
  }

  findHtmlFiles(dir, files = []) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.findHtmlFiles(fullPath, files);
      } else if (item.endsWith('.html')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  async auditFile(filePath) {
    const relativePath = path.relative(distDir, filePath);
    const content = fs.readFileSync(filePath, 'utf-8');
    const dom = new JSDOM(content);
    const document = dom.window.document;
    
    console.log(`📄 Auditing: ${relativePath}`);
    
    const pageResults = {
      path: relativePath,
      errors: [],
      warnings: [],
      passed: true
    };
    
    // Audit meta tags
    this.auditMetaTags(document, pageResults);
    
    // Audit headings
    this.auditHeadings(document, pageResults);
    
    // Audit images
    this.auditImages(document, pageResults);
    
    // Audit structured data
    this.auditStructuredData(document, pageResults);
    
    // Audit links
    this.auditLinks(document, pageResults);
    
    // Audit semantic HTML
    this.auditSemanticHTML(document, pageResults);
    
    if (pageResults.errors.length > 0) {
      this.results.failedPages++;
      pageResults.passed = false;
      console.log(`  ❌ ${pageResults.errors.length} errors found`);
    } else {
      this.results.passedPages++;
      console.log(`  ✅ Passed`);
    }
    
    if (pageResults.warnings.length > 0) {
      console.log(`  ⚠️  ${pageResults.warnings.length} warnings`);
    }
    
    this.results.errors.push(...pageResults.errors.map(e => ({ ...e, page: relativePath })));
    this.results.warnings.push(...pageResults.warnings.map(w => ({ ...w, page: relativePath })));
    
    console.log('');
  }

  auditMetaTags(document, results) {
    const title = document.title;
    const description = document.querySelector('meta[name="description"]')?.content;
    const canonical = document.querySelector('link[rel="canonical"]')?.href;
    const ogTitle = document.querySelector('meta[property="og:title"]')?.content;
    const ogDescription = document.querySelector('meta[property="og:description"]')?.content;
    const ogImage = document.querySelector('meta[property="og:image"]')?.content;
    const robots = document.querySelector('meta[name="robots"]')?.content;
    
    // Title validation
    if (!title) {
      results.errors.push({ type: 'meta', message: 'Missing page title' });
    } else {
      if (title.length < SEO_RULES.title.minLength) {
        results.warnings.push({ type: 'meta', message: `Title too short (${title.length} chars, min ${SEO_RULES.title.minLength})` });
      }
      if (title.length > SEO_RULES.title.maxLength) {
        results.warnings.push({ type: 'meta', message: `Title too long (${title.length} chars, max ${SEO_RULES.title.maxLength})` });
      }
    }
    
    // Description validation
    if (!description) {
      results.errors.push({ type: 'meta', message: 'Missing meta description' });
    } else {
      if (description.length < SEO_RULES.description.minLength) {
        results.warnings.push({ type: 'meta', message: `Description too short (${description.length} chars, min ${SEO_RULES.description.minLength})` });
      }
      if (description.length > SEO_RULES.description.maxLength) {
        results.warnings.push({ type: 'meta', message: `Description too long (${description.length} chars, max ${SEO_RULES.description.maxLength})` });
      }
    }
    
    // Essential meta tags
    if (!canonical) {
      results.errors.push({ type: 'meta', message: 'Missing canonical URL' });
    }
    
    if (!ogTitle) {
      results.warnings.push({ type: 'meta', message: 'Missing Open Graph title' });
    }
    
    if (!ogDescription) {
      results.warnings.push({ type: 'meta', message: 'Missing Open Graph description' });
    }
    
    if (!ogImage) {
      results.warnings.push({ type: 'meta', message: 'Missing Open Graph image' });
    }
    
    if (!robots) {
      results.warnings.push({ type: 'meta', message: 'Missing robots meta tag' });
    }
  }

  auditHeadings(document, results) {
    const h1s = document.querySelectorAll('h1');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    // H1 validation
    if (h1s.length === 0) {
      results.errors.push({ type: 'headings', message: 'Missing H1 tag' });
    } else if (h1s.length > 1) {
      results.warnings.push({ type: 'headings', message: `Multiple H1 tags found (${h1s.length})` });
    }
    
    // Heading hierarchy validation
    let previousLevel = 0;
    for (const heading of headings) {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > previousLevel + 1) {
        results.warnings.push({ 
          type: 'headings', 
          message: `Heading hierarchy skip: ${heading.tagName} after H${previousLevel}` 
        });
      }
      previousLevel = level;
    }
  }

  auditImages(document, results) {
    const images = document.querySelectorAll('img');
    
    for (const img of images) {
      const alt = img.getAttribute('alt');
      const src = img.getAttribute('src');
      
      if (!alt && alt !== '') {
        results.errors.push({ 
          type: 'images', 
          message: `Image missing alt attribute: ${src}` 
        });
      } else if (alt && alt.length > SEO_RULES.images.maxAltLength) {
        results.warnings.push({ 
          type: 'images', 
          message: `Alt text too long (${alt.length} chars): ${src}` 
        });
      }
    }
  }

  auditStructuredData(document, results) {
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');
    
    if (scripts.length === 0) {
      results.warnings.push({ type: 'structured-data', message: 'No structured data found' });
      return;
    }
    
    for (const script of scripts) {
      try {
        const data = JSON.parse(script.textContent);
        if (!data['@context'] || !data['@type']) {
          results.warnings.push({ 
            type: 'structured-data', 
            message: 'Structured data missing @context or @type' 
          });
        }
      } catch (e) {
        results.errors.push({ 
          type: 'structured-data', 
          message: 'Invalid JSON in structured data' 
        });
      }
    }
  }

  auditLinks(document, results) {
    const links = document.querySelectorAll('a[href]');
    let internalLinks = 0;
    let externalLinks = 0;
    
    for (const link of links) {
      const href = link.getAttribute('href');
      
      if (href.startsWith('http')) {
        externalLinks++;
        if (!link.getAttribute('rel')?.includes('noopener')) {
          results.warnings.push({ 
            type: 'links', 
            message: `External link missing rel="noopener": ${href}` 
          });
        }
      } else if (href.startsWith('/') || !href.includes('://')) {
        internalLinks++;
      }
    }
    
    results.linkStats = { internal: internalLinks, external: externalLinks };
  }

  auditSemanticHTML(document, results) {
    const semanticElements = ['header', 'main', 'nav', 'section', 'article', 'aside', 'footer'];
    const foundElements = [];
    
    for (const element of semanticElements) {
      if (document.querySelector(element)) {
        foundElements.push(element);
      }
    }
    
    if (!foundElements.includes('main')) {
      results.warnings.push({ type: 'semantic', message: 'Missing <main> element' });
    }
    
    if (foundElements.length < 3) {
      results.warnings.push({ 
        type: 'semantic', 
        message: `Limited semantic HTML usage (${foundElements.length}/7 elements)` 
      });
    }
  }

  generateReport() {
    console.log('📊 SEO Audit Report');
    console.log('='.repeat(50));
    console.log(`Total Pages Audited: ${this.results.totalPages}`);
    console.log(`Passed: ${this.results.passedPages} (${Math.round(this.results.passedPages / this.results.totalPages * 100)}%)`);
    console.log(`Failed: ${this.results.failedPages} (${Math.round(this.results.failedPages / this.results.totalPages * 100)}%)`);
    console.log(`Total Errors: ${this.results.errors.length}`);
    console.log(`Total Warnings: ${this.results.warnings.length}`);
    console.log('');
    
    if (this.results.errors.length > 0) {
      console.log('❌ ERRORS:');
      console.log('-'.repeat(30));
      for (const error of this.results.errors) {
        console.log(`  ${error.page}: ${error.message}`);
      }
      console.log('');
    }
    
    if (this.results.warnings.length > 0) {
      console.log('⚠️  WARNINGS:');
      console.log('-'.repeat(30));
      for (const warning of this.results.warnings) {
        console.log(`  ${warning.page}: ${warning.message}`);
      }
      console.log('');
    }
    
    // Generate summary by error type
    const errorsByType = {};
    const warningsByType = {};
    
    for (const error of this.results.errors) {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
    }
    
    for (const warning of this.results.warnings) {
      warningsByType[warning.type] = (warningsByType[warning.type] || 0) + 1;
    }
    
    console.log('📈 SUMMARY BY CATEGORY:');
    console.log('-'.repeat(30));
    console.log('Errors by type:', errorsByType);
    console.log('Warnings by type:', warningsByType);
    console.log('');
    
    const overallScore = Math.round((this.results.passedPages / this.results.totalPages) * 100);
    console.log(`🎯 Overall SEO Score: ${overallScore}/100`);
    
    if (overallScore >= 90) {
      console.log('🎉 Excellent SEO implementation!');
    } else if (overallScore >= 80) {
      console.log('👍 Good SEO implementation with room for improvement');
    } else if (overallScore >= 70) {
      console.log('⚠️  SEO implementation needs attention');
    } else {
      console.log('❌ SEO implementation requires significant improvements');
    }
  }
}

// Run the audit
const auditor = new SEOAuditor();
auditor.auditDirectory().catch(console.error);
