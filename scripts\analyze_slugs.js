import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DATA_FILE = path.join(__dirname, '../data_set.csv');

// Helper function to slugify strings (copied from generate-rest-area-md.js)
function slugify(text) {
  return text
    .toString()
    .toLowerCase()
    .replace(/ł/g, 'l')
    .replace(/ć/g, 'c')
    .replace(/ó/g, 'o')
    .replace(/ę/g, 'e')
    .replace(/ą/g, 'a')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
}

// Function to parse CSV content (copied from generate-rest-area-md.js)
function parseCsv(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',').map(header => header.trim());
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(value => value.trim());
    const row = {};
    for (let j = 0; j < headers.length; j++) {
      row[headers[j]] = values[j];
    }
    data.push(row);
  }
  return data;
}

async function analyzeSlugs() {
  try {
    const csvContent = fs.readFileSync(DATA_FILE, 'utf-8');
    const data = parseCsv(csvContent);

    const slugMap = {}; // Stores slug -> [array of row indices]

    data.forEach((row, index) => {
      const slug = slugify(`${row.rest_area_id}-${row.road_class}${row.road_number}-${row.travel_direction}`);
      
      if (!slugMap[slug]) {
        slugMap[slug] = [];
      }
      slugMap[slug].push(index); // Store original index
    });

    const duplicateSlugs = {};
    let uniqueSlugsCount = 0;

    for (const slug in slugMap) {
      if (slugMap[slug].length > 1) {
        duplicateSlugs[slug] = slugMap[slug].map(index => ({
          rowData: data[index],
          originalIndex: index + 1 // +1 for 1-based line numbers
        }));
      }
      uniqueSlugsCount++;
    }

    console.log('Total unique slugs generated:', uniqueSlugsCount);
    console.log('Total rows in CSV:', data.length);

    if (Object.keys(duplicateSlugs).length > 0) {
      console.log('\n--- Duplicate Slugs Found ---');
      for (const slug in duplicateSlugs) {
        console.log(`\nSlug: "${slug}" (Occurrences: ${duplicateSlugs[slug].length})`);
        duplicateSlugs[slug].forEach(item => {
          console.log(`  Row Index: ${item.originalIndex}, rest_area_id: "${item.rowData.rest_area_id}", road_class: "${item.rowData.road_class}", road_number: "${item.rowData.road_number}", travel_direction: "${item.rowData.travel_direction}", location: "${item.rowData.location}", latitude: "${item.rowData.latitude}", longitude: "${item.rowData.longitude}"`);
        });
      }
    } else {
      console.log('\nNo duplicate slugs found with the current logic.');
    }

  } catch (error) {
    console.error('Error analyzing slugs:', error);
  }
}

analyzeSlugs();