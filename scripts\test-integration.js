#!/usr/bin/env node

/**
 * Integration Test Script
 * 
 * This script tests the complete Google Sheets to Markdown integration
 * with sample data to ensure everything works correctly.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Sample test data
const SAMPLE_DATA = {
  en: [
    {
      rest_area_id: 'Test Area 1',
      title: 'Test Rest Area 1',
      latitude: '52.2297',
      longitude: '21.0122',
      road_class: 'A',
      road_number: '2',
      km_marker: '100+500',
      region: 'Mazowieckie',
      location: 'Warsaw',
      country: 'Poland',
      description_short: 'Test rest area for integration testing',
      toilets_available: 'yes',
      wifi: 'no',
      gas_station_available: 'yes',
      restaurant_bistro_available: 'no',
      showers_available: 'yes',
      ev_charging_station: 'yes',
      parking_spaces_cars: '50',
      parking_spaces_trucks_tir: '20',
      parking_spaces_buses: '5',
      language: 'en'
    }
  ],
  pl: [
    {
      rest_area_id: 'Obszar Testowy 1',
      title: 'Testowy Obszar Wypoczynku 1',
      latitude: '52.2297',
      longitude: '21.0122',
      road_class: 'A',
      road_number: '2',
      km_marker: '100+500',
      region: 'Mazowieckie',
      location: 'Warszawa',
      country: 'Polska',
      description_short: 'Testowy obszar wypoczynku do testów integracji',
      toilets_available: 'yes',
      wifi: 'no',
      gas_station_available: 'yes',
      restaurant_bistro_available: 'no',
      showers_available: 'yes',
      ev_charging_station: 'yes',
      parking_spaces_cars: '50',
      parking_spaces_trucks_tir: '20',
      parking_spaces_buses: '5',
      language: 'pl'
    }
  ]
};

async function setupTestEnvironment() {
  console.log('🔧 Setting up test environment...');
  
  // Create test directories
  const testDir = path.join(__dirname, '../temp/test-integration');
  const exportDir = path.join(testDir, 'sheets-export');
  const backupDir = path.join(testDir, 'content-backup');
  
  await fs.mkdir(exportDir, { recursive: true });
  await fs.mkdir(backupDir, { recursive: true });
  
  // Create sample JSON files
  for (const [language, data] of Object.entries(SAMPLE_DATA)) {
    const filePath = path.join(exportDir, `rest-areas-${language}.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    console.log(`✅ Created test data: ${filePath}`);
  }
  
  // Create export summary
  const summary = {
    timestamp: new Date().toISOString(),
    languages: {
      en: { recordCount: SAMPLE_DATA.en.length, errors: 0, warnings: 0 },
      pl: { recordCount: SAMPLE_DATA.pl.length, errors: 0, warnings: 0 }
    },
    summary: {
      totalRecords: SAMPLE_DATA.en.length + SAMPLE_DATA.pl.length,
      errors: [],
      warnings: []
    }
  };
  
  const summaryPath = path.join(exportDir, 'export-summary.json');
  await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2), 'utf-8');
  console.log(`✅ Created export summary: ${summaryPath}`);
  
  return { testDir, exportDir };
}

async function testMarkdownGeneration(testDir) {
  console.log('\n📝 Testing markdown generation...');
  
  try {
    // Temporarily modify CONFIG in generate-from-json.js
    const { generateFromJson } = await import('./generate-from-json.js');
    
    // Create test output directories
    const testOutputDirs = {
      en: path.join(testDir, 'output/en'),
      pl: path.join(testDir, 'output/pl')
    };
    
    await fs.mkdir(testOutputDirs.en, { recursive: true });
    await fs.mkdir(testOutputDirs.pl, { recursive: true });
    
    // Test generation for each language
    const results = {};
    
    for (const language of ['en', 'pl']) {
      const inputFile = path.join(testDir, 'sheets-export', `rest-areas-${language}.json`);
      
      // Temporarily override CONFIG for testing
      const originalConfig = process.env.TEST_OUTPUT_DIR;
      process.env.TEST_OUTPUT_DIR = testOutputDirs[language];
      
      try {
        const result = await generateFromJson(inputFile, language);
        results[language] = result;
        console.log(`✅ Generated ${result.generated} files for ${language}`);
        
        if (result.errors.length > 0) {
          console.warn(`⚠️  Errors in ${language}:`, result.errors);
        }
        
      } catch (error) {
        console.error(`❌ Failed to generate ${language}:`, error.message);
        results[language] = { generated: 0, errors: [error.message] };
      }
      
      // Restore original config
      if (originalConfig) {
        process.env.TEST_OUTPUT_DIR = originalConfig;
      } else {
        delete process.env.TEST_OUTPUT_DIR;
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Markdown generation test failed:', error.message);
    return null;
  }
}

async function validateGeneratedFiles(testDir) {
  console.log('\n🔍 Validating generated files...');
  
  const outputDir = path.join(testDir, 'output');
  let totalFiles = 0;
  let validFiles = 0;
  
  for (const language of ['en', 'pl']) {
    const langDir = path.join(outputDir, language);
    
    try {
      const files = await fs.readdir(langDir);
      const mdFiles = files.filter(file => file.endsWith('.md'));
      
      console.log(`📁 Found ${mdFiles.length} markdown files for ${language}`);
      totalFiles += mdFiles.length;
      
      for (const file of mdFiles) {
        const filePath = path.join(langDir, file);
        const content = await fs.readFile(filePath, 'utf-8');
        
        // Basic validation
        const hasFrontmatter = content.startsWith('---') && content.includes('---\n\n');
        const hasTitle = content.includes('title:');
        const hasCoordinates = content.includes('coordinates:');
        const hasContent = content.split('---\n\n')[1]?.trim().length > 0;
        
        if (hasFrontmatter && hasTitle && hasCoordinates && hasContent) {
          validFiles++;
          console.log(`✅ Valid: ${file}`);
        } else {
          console.error(`❌ Invalid: ${file}`);
          console.error(`   Frontmatter: ${hasFrontmatter}`);
          console.error(`   Title: ${hasTitle}`);
          console.error(`   Coordinates: ${hasCoordinates}`);
          console.error(`   Content: ${hasContent}`);
        }
      }
      
    } catch (error) {
      console.error(`❌ Failed to validate ${language} files:`, error.message);
    }
  }
  
  console.log(`\n📊 Validation Summary: ${validFiles}/${totalFiles} files are valid`);
  return { totalFiles, validFiles };
}

async function testContentLayerCompatibility(testDir) {
  console.log('\n🏗️  Testing Content Layer compatibility...');
  
  try {
    // Copy generated files to actual content directory temporarily
    const sourceDir = path.join(testDir, 'output');
    const targetDir = path.join(__dirname, '../src/content/rest-areas');
    const backupDir = path.join(testDir, 'original-backup');
    
    // Create backup of original content
    await fs.mkdir(backupDir, { recursive: true });
    
    try {
      const originalEn = path.join(targetDir, 'en');
      const originalPl = path.join(targetDir, 'pl');
      
      // Backup existing content
      try {
        await fs.cp(originalEn, path.join(backupDir, 'en'), { recursive: true });
        console.log('📦 Backed up existing EN content');
      } catch (error) {
        console.log('ℹ️  No existing EN content to backup');
      }
      
      try {
        await fs.cp(originalPl, path.join(backupDir, 'pl'), { recursive: true });
        console.log('📦 Backed up existing PL content');
      } catch (error) {
        console.log('ℹ️  No existing PL content to backup');
      }
      
      // Copy test files
      await fs.cp(path.join(sourceDir, 'en'), originalEn, { recursive: true });
      await fs.cp(path.join(sourceDir, 'pl'), originalPl, { recursive: true });
      console.log('📁 Copied test files to content directory');
      
      // Test Astro build (this would require Astro to be properly configured)
      console.log('🏗️  Testing Astro compatibility...');
      
      // For now, just validate that the files can be read by the content system
      try {
        // This is a simplified test - in a real scenario, you'd run `astro build`
        console.log('✅ Content Layer compatibility test passed (simplified)');
        
        // In a full test, you would:
        // 1. Import Astro's getCollection function
        // 2. Try to load the content
        // 3. Validate the schema
        // 4. Run a build test
        
      } catch (error) {
        console.error('❌ Content Layer compatibility test failed:', error.message);
      }
      
      // Restore original content
      try {
        await fs.rm(originalEn, { recursive: true, force: true });
        await fs.rm(originalPl, { recursive: true, force: true });
        
        try {
          await fs.cp(path.join(backupDir, 'en'), originalEn, { recursive: true });
        } catch (error) {
          // No backup to restore
        }
        
        try {
          await fs.cp(path.join(backupDir, 'pl'), originalPl, { recursive: true });
        } catch (error) {
          // No backup to restore
        }
        
        console.log('🔄 Restored original content');
      } catch (error) {
        console.error('⚠️  Failed to restore original content:', error.message);
      }
      
    } catch (error) {
      console.error('❌ Content Layer test failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Content Layer compatibility test failed:', error.message);
  }
}

async function cleanup(testDir) {
  console.log('\n🧹 Cleaning up test environment...');
  
  try {
    await fs.rm(testDir, { recursive: true, force: true });
    console.log('✅ Test environment cleaned up');
  } catch (error) {
    console.error('⚠️  Failed to clean up test environment:', error.message);
  }
}

async function main() {
  console.log('🧪 Google Sheets Integration Test');
  console.log('=================================\n');
  
  let testDir;
  
  try {
    // Setup test environment
    const setup = await setupTestEnvironment();
    testDir = setup.testDir;
    
    // Test markdown generation
    const generationResults = await testMarkdownGeneration(testDir);
    
    if (!generationResults) {
      throw new Error('Markdown generation test failed');
    }
    
    // Validate generated files
    const validationResults = await validateGeneratedFiles(testDir);
    
    // Test Content Layer compatibility
    await testContentLayerCompatibility(testDir);
    
    // Print summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 INTEGRATION TEST SUMMARY');
    console.log('='.repeat(50));
    
    let totalGenerated = 0;
    let totalErrors = 0;
    
    Object.entries(generationResults).forEach(([lang, result]) => {
      console.log(`${lang.toUpperCase()}: ${result.generated} files, ${result.errors.length} errors`);
      totalGenerated += result.generated;
      totalErrors += result.errors.length;
    });
    
    console.log(`\nTotal Generated: ${totalGenerated}`);
    console.log(`Total Errors: ${totalErrors}`);
    console.log(`Valid Files: ${validationResults.validFiles}/${validationResults.totalFiles}`);
    
    if (totalErrors === 0 && validationResults.validFiles === validationResults.totalFiles) {
      console.log('\n🎉 All integration tests passed!');
      console.log('\n✅ The Google Sheets sync system is working correctly.');
      console.log('\n🚀 Next steps:');
      console.log('   1. Configure your Google Sheets');
      console.log('   2. Set up GitHub repository secrets');
      console.log('   3. Run the validation script: npm run sheets:validate');
      console.log('   4. Test with real data: npm run sheets:sync');
    } else {
      console.log('\n❌ Some integration tests failed.');
      console.log('Please review the errors above and fix any issues.');
    }
    
  } catch (error) {
    console.error('\n💥 Integration test failed:', error.message);
    process.exit(1);
  } finally {
    if (testDir) {
      await cleanup(testDir);
    }
  }
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as runIntegrationTest };
