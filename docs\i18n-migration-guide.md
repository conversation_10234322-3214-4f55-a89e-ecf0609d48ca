# i18n Migration Guide

This document outlines the changes made during the multilingual implementation and provides guidance for developers working with the updated codebase.

## 🔄 What Changed

### URL Structure Changes

**Before:**
- All pages used English URLs only
- Poland pages: `/pl/` (conflicted with Polish language)

**After:**
- English (default): `/poland/` for country pages
- Polish: `/pl/` for language, `/pl/polska/` for country pages
- Clear separation between language locales and country-specific content

### File Structure Changes

**New Files Added:**
```
src/
├── i18n/
│   ├── en.json                     # English translations
│   ├── pl.json                     # Polish translations
│   └── utils.ts                    # i18n utility functions
├── components/
│   └── LanguageSelector.astro      # Language switching component
└── pages/pl/                       # Polish language pages
    ├── index.astro                 # Polish homepage
    ├── polska/                     # Polish Poland pages
    │   ├── index.astro
    │   └── [...locationSlug].astro
    └── rest-areas/
        └── index.astro
```

**Modified Files:**
- `astro.config.mjs` - Added i18n configuration
- `src/layouts/Layout.astro` - Added language detection
- `src/components/Header.astro` - Added translations and language selector
- `src/pages/index.astro` - Added translations
- `src/pages/poland/` - Moved from `/pl/` and updated

### Configuration Changes

**Astro Configuration:**
```javascript
// astro.config.mjs - NEW
export default defineConfig({
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'pl'],
    routing: {
      prefixDefaultLocale: false
    }
  }
});
```

## 🚨 Breaking Changes

### URL Changes

| Old URL | New URL | Status |
|---------|---------|--------|
| `/pl/` | `/poland/` (English) or `/pl/` (Polish homepage) | ⚠️ Changed |
| `/pl/mazowieckie/` | `/poland/mazowieckie/` (EN) or `/pl/polska/mazowieckie/` (PL) | ⚠️ Changed |

### Component Changes

**Before:**
```astro
<!-- Hard-coded text -->
<h1>EU Highway Rest Areas</h1>
<a href="/pl/">Browse Locations</a>
```

**After:**
```astro
<!-- Translated text -->
---
import { getLangFromUrl, useTranslations } from '../i18n/utils';
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---
<h1>{t('homepage.title')}</h1>
<a href="/poland/">{t('nav.browseLocations')}</a>
```

## 🔧 Migration Steps for Developers

### 1. Update Hard-coded Text

**Find and replace hard-coded strings:**
```astro
<!-- OLD -->
<h1>EU Highway Rest Areas</h1>

<!-- NEW -->
---
const t = useTranslations(currentLang);
---
<h1>{t('homepage.title')}</h1>
```

### 2. Update URL References

**Update links to Poland pages:**
```astro
<!-- OLD -->
<a href="/pl/">Browse Poland</a>

<!-- NEW -->
<a href="/poland/">Browse Poland</a>  <!-- English -->
<a href="/pl/polska/">Browse Poland</a>  <!-- Polish -->
```

### 3. Add Language Detection to New Pages

**Template for new pages:**
```astro
---
import Layout from '../layouts/Layout.astro';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<Layout title={t('page.title')}>
  <h1>{t('page.heading')}</h1>
  <!-- Content with translations -->
</Layout>
```

### 4. Update Navigation Components

**Add language selector to headers:**
```astro
---
import LanguageSelector from './LanguageSelector.astro';
---

<header>
  <nav><!-- existing nav --></nav>
  <LanguageSelector />
</header>
```

## 📋 Checklist for Existing Components

When updating existing components, ensure:

- [ ] Import i18n utilities at the top
- [ ] Add language detection: `const currentLang = getLangFromUrl(Astro.url);`
- [ ] Add translation function: `const t = useTranslations(currentLang);`
- [ ] Replace hard-coded text with `t('key')`
- [ ] Update URLs to use new structure
- [ ] Add translation keys to both `en.json` and `pl.json`

## 🔍 Testing Your Changes

### 1. Verify URLs Work
```bash
# Test English URLs
curl -I http://localhost:4321/poland/
curl -I http://localhost:4321/rest-areas/

# Test Polish URLs
curl -I http://localhost:4321/pl/
curl -I http://localhost:4321/pl/polska/
curl -I http://localhost:4321/pl/rest-areas/

# Verify old URLs return 404
curl -I http://localhost:4321/pl/poland/  # Should be 404
```

### 2. Test Language Switching
- Navigate to any page
- Use language selector to switch languages
- Verify you stay on the equivalent page in the new language
- Check that URLs use correct localized slugs

### 3. Verify Translations
- Check all text is translated (no English text on Polish pages)
- Look for missing translation keys (they'll show as the key itself)
- Test special characters and Polish diacritics

## 🚫 Common Migration Mistakes

### 1. Forgetting Language Detection
```astro
<!-- WRONG - No language detection -->
<h1>Hard-coded title</h1>

<!-- CORRECT -->
---
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---
<h1>{t('page.title')}</h1>
```

### 2. Using Old URL Structure
```astro
<!-- WRONG - Old URL structure -->
<a href="/pl/mazowieckie/">Region</a>

<!-- CORRECT - New URL structure -->
<a href="/poland/mazowieckie/">Region</a>  <!-- English -->
<a href="/pl/polska/mazowieckie/">Region</a>  <!-- Polish -->
```

### 3. Missing Translation Keys
```json
// WRONG - Only in English file
// en.json
{ "page": { "title": "Title" } }

// pl.json
{ }

// CORRECT - In both files
// en.json
{ "page": { "title": "Title" } }

// pl.json  
{ "page": { "title": "Tytuł" } }
```

### 4. Incorrect Import Paths
```astro
<!-- WRONG - Incorrect path for Polish pages -->
import Layout from '../layouts/Layout.astro';  // From pl/polska/page.astro

<!-- CORRECT -->
import Layout from '../../../layouts/Layout.astro';  // From pl/polska/page.astro
```

## 🔄 Backward Compatibility

### Redirects for Old URLs

The old `/pl/poland/` URLs now return 404. If you need to maintain backward compatibility, consider adding redirects:

```javascript
// In your deployment configuration or server
// Redirect old URLs to new structure
'/pl/poland/*' → '/poland/*'  // For English users
'/pl/poland/*' → '/pl/polska/*'  // For Polish users (with language detection)
```

### Data Migration

If you have any stored URLs or references to the old structure:
- Update database records with old URLs
- Update any external links or bookmarks
- Update sitemap.xml with new URL structure

## 📈 Performance Considerations

### Build Time
- Translation files are loaded at build time
- No runtime performance impact for translations
- Static generation works normally

### Bundle Size
- Translation files add minimal size
- Only used translations are included in each page
- No client-side translation libraries needed

## 🎯 Next Steps

After migration:

1. **Update documentation** for your specific components
2. **Train team members** on new translation workflow
3. **Set up translation workflow** for content updates
4. **Consider adding more languages** using the established pattern
5. **Monitor analytics** for language usage patterns

## 📞 Getting Help

If you encounter issues during migration:

1. Check the [Complete Implementation Guide](i18n-implementation.md)
2. Review the [Quick Reference Guide](i18n-quick-reference.md)
3. Test with provided URL examples
4. Verify your translation file structure matches the examples

## ✅ Migration Complete Checklist

- [ ] All hard-coded text replaced with translations
- [ ] All URLs updated to new structure
- [ ] Language detection added to all pages
- [ ] Translation keys added to both language files
- [ ] Language selector added to navigation
- [ ] All pages tested in both languages
- [ ] URL redirects configured (if needed)
- [ ] Team trained on new workflow

---

**Migration Status:** Complete when all items checked
**Estimated Time:** 2-4 hours for existing codebase
**Risk Level:** Medium (URL structure changes)
