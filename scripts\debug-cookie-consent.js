#!/usr/bin/env node

/**
 * <PERSON>ie Consent Debug Script
 * Helps troubleshoot cookie consent banner issues
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const projectRoot = process.cwd();

function checkFile(filePath, description) {
  try {
    const content = readFileSync(join(projectRoot, filePath), 'utf-8');
    console.log(`✅ ${description}: Found`);
    return content;
  } catch (error) {
    console.log(`❌ ${description}: Missing`);
    return null;
  }
}

function checkEnvVar(content, varName, expectedValue = null) {
  const regex = new RegExp(`^${varName}=(.*)$`, 'm');
  const match = content.match(regex);
  
  if (match) {
    const value = match[1];
    const isCorrect = expectedValue ? value === expectedValue : value !== '';
    console.log(`  ${isCorrect ? '✅' : '⚠️'} ${varName}=${value}`);
    return value;
  } else {
    console.log(`  ❌ ${varName}: Not found`);
    return null;
  }
}

function checkContent(content, searchTerm, description) {
  if (content && content.includes(searchTerm)) {
    console.log(`  ✅ ${description}: Found`);
    return true;
  } else {
    console.log(`  ❌ ${description}: Missing`);
    return false;
  }
}

console.log('🍪 Cookie Consent Banner Troubleshooting\n');

// 1. Check environment file
console.log('📄 Environment Configuration:');
const envFile = checkFile('.env', 'Environment file');
if (envFile) {
  checkEnvVar(envFile, 'PUBLIC_ENABLE_COOKIE_CONSENT', 'true');
  checkEnvVar(envFile, 'PUBLIC_ENABLE_DEV_ANALYTICS');
  checkEnvVar(envFile, 'PUBLIC_ENABLE_ANALYTICS', 'true');
  checkEnvVar(envFile, 'PUBLIC_GA4_MEASUREMENT_ID');
} else {
  console.log('  ⚠️ Create .env file from .env.example');
}

console.log('\n📁 Component Files:');

// 2. Check CookieConsent component
const cookieConsentFile = checkFile('src/components/CookieConsent.astro', 'CookieConsent component');
if (cookieConsentFile) {
  checkContent(cookieConsentFile, 'shouldShowConsent', 'Visibility condition');
  checkContent(cookieConsentFile, 'x-data="cookieConsent()"', 'Alpine.js integration');
  checkContent(cookieConsentFile, 'x-show="!consentGiven"', 'Conditional visibility');
  checkContent(cookieConsentFile, 'z-50', 'Z-index styling');
}

// 3. Check TrackingManager component
const trackingManagerFile = checkFile('src/components/TrackingManager.astro', 'TrackingManager component');
if (trackingManagerFile) {
  checkContent(trackingManagerFile, 'import CookieConsent', 'CookieConsent import');
  checkContent(trackingManagerFile, '<CookieConsent', 'CookieConsent usage');
  checkContent(trackingManagerFile, 'enableCookieConsent', 'Cookie consent prop');
}

// 4. Check Layout integration
const layoutFile = checkFile('src/layouts/Layout.astro', 'Layout component');
if (layoutFile) {
  checkContent(layoutFile, 'import TrackingManager', 'TrackingManager import');
  checkContent(layoutFile, '<TrackingManager', 'TrackingManager usage');
  checkContent(layoutFile, 'enableCookieConsent', 'Cookie consent prop passing');
}

// 5. Check Alpine.js configuration
const astroConfigFile = checkFile('astro.config.mjs', 'Astro configuration');
if (astroConfigFile) {
  checkContent(astroConfigFile, 'alpinejs()', 'Alpine.js integration');
}

// 6. Check translations
console.log('\n🌐 Translation Files:');
const enTranslations = checkFile('src/i18n/en.json', 'English translations');
if (enTranslations) {
  checkContent(enTranslations, 'cookieConsent', 'Cookie consent translations (EN)');
}

const plTranslations = checkFile('src/i18n/pl.json', 'Polish translations');
if (plTranslations) {
  checkContent(plTranslations, 'cookieConsent', 'Cookie consent translations (PL)');
}

// 7. Check debug components
console.log('\n🔧 Debug Tools:');
checkFile('src/components/CookieConsentDebug.astro', 'Debug component');
checkFile('src/pages/test-cookie-consent.astro', 'Test page');

console.log('\n🔍 Common Issues & Solutions:');

console.log('\n1. Banner not showing in development:');
console.log('   Solution: Set PUBLIC_ENABLE_DEV_ANALYTICS=true in .env');

console.log('\n2. Banner not showing in production:');
console.log('   Solution: Ensure PUBLIC_ENABLE_COOKIE_CONSENT=true in .env');

console.log('\n3. Banner hidden by existing consent:');
console.log('   Solution: Clear localStorage or visit /test-cookie-consent');

console.log('\n4. Alpine.js not working:');
console.log('   Solution: Check browser console for JavaScript errors');

console.log('\n5. Z-index issues:');
console.log('   Solution: Check CSS conflicts, banner should have z-index: 50');

console.log('\n6. Environment variables not loading:');
console.log('   Solution: Restart dev server after changing .env');

console.log('\n📋 Debugging Steps:');
console.log('1. Visit /test-cookie-consent for detailed debugging');
console.log('2. Open browser developer tools');
console.log('3. Check console for "🍪 Cookie Consent Debug" messages');
console.log('4. Look for Alpine.js initialization messages');
console.log('5. Verify banner element exists in DOM');
console.log('6. Check localStorage for existing consent');

console.log('\n🚀 Quick Fixes:');
console.log('• Clear browser localStorage: localStorage.clear()');
console.log('• Force show banner: localStorage.removeItem("cookie-consent")');
console.log('• Enable dev mode: PUBLIC_ENABLE_DEV_ANALYTICS=true');
console.log('• Check visibility: document.getElementById("cookie-consent-banner")');

console.log('\n✨ Test Commands:');
console.log('• npm run dev (start development server)');
console.log('• Visit http://localhost:4321/test-cookie-consent');
console.log('• Open browser console and look for debug messages');

// Check if test page exists and suggest creating it
if (!existsSync(join(projectRoot, 'src/pages/test-cookie-consent.astro'))) {
  console.log('\n⚠️ Test page not found. Create it with:');
  console.log('   Copy the test page from the implementation above');
}

console.log('\n🎯 Expected Behavior:');
console.log('The cookie consent banner should appear when:');
console.log('• Environment: PUBLIC_ENABLE_COOKIE_CONSENT=true');
console.log('• Mode: Production OR (Development + PUBLIC_ENABLE_DEV_ANALYTICS=true)');
console.log('• Storage: No existing consent in localStorage');
console.log('• DOM: Banner element exists with proper Alpine.js attributes');
console.log('• Styling: Banner has z-index: 50 and proper positioning');

console.log('\n🔧 If banner still not showing:');
console.log('1. Restart the development server');
console.log('2. Clear browser cache and localStorage');
console.log('3. Check browser console for errors');
console.log('4. Verify all environment variables are set correctly');
console.log('5. Test in incognito/private browsing mode');

console.log('\n✅ Troubleshooting complete!');
console.log('Visit /test-cookie-consent for interactive debugging.');
