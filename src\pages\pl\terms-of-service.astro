---
import Layout from '../../layouts/Layout.astro';
import HeroSection from '../../components/HeroSection.astro';
import { getLangFromUrl, useTranslations } from '../../i18n/utils';

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<Layout title={`${t('termsOfService.title')} - stops24.com`}>
  <main>
    <HeroSection
      title={t('termsOfService.title')}
      highlightText={t('termsOfService.title')}
      description={t('termsOfService.description')}
    />

    <section class="py-12 bg-white dark:bg-gray-800">
      <div class="container-custom">
        <div class="prose prose-lg dark:prose-invert max-w-4xl mx-auto">
          <h2>{t('termsOfService.introduction')}</h2>
          <p>{t('termsOfService.introductionText')}</p>
          
          <h2>{t('termsOfService.definitions')}</h2>
          <p>{t('termsOfService.definitionsText')}</p>
          
          <h2>{t('termsOfService.acceptance')}</h2>
          <p>{t('termsOfService.acceptanceText')}</p>
          
          <h2>{t('termsOfService.changes')}</h2>
          <p>{t('termsOfService.changesText')}</p>
          
          <h2>{t('termsOfService.eligibility')}</h2>
          <p>{t('termsOfService.eligibilityText')}</p>
          
          <h2>{t('termsOfService.userAccounts')}</h2>
          <p>{t('termsOfService.userAccountsText')}</p>
          
          <h2>{t('termsOfService.donations')}</h2>
          <p>{t('termsOfService.donationsText')}</p>
          <p>{t('termsOfService.donationsText2')}</p>
          
          <h2>{t('termsOfService.volunteerServices')}</h2>
          <p>{t('termsOfService.volunteerServicesText')}</p>
          
          <h2>{t('termsOfService.userContent')}</h2>
          <p>{t('termsOfService.userContentText')}</p>
          <p>{t('termsOfService.userContentText2')}</p>
          
          <h2>{t('termsOfService.prohibitedConduct')}</h2>
          <p>{t('termsOfService.prohibitedConductText')}</p>
          
          <h2>{t('termsOfService.intellectualProperty')}</h2>
          <p>{t('termsOfService.intellectualPropertyText')}</p>
          <p>{t('termsOfService.intellectualPropertyText2')}</p>
          
          <h2>{t('termsOfService.dmcaPolicy')}</h2>
          <p>{t('termsOfService.dmcaPolicyText')}</p>
          <p>{t('termsOfService.dmcaEmail')}</p>
          
          <h2>{t('termsOfService.privacyPolicy')}</h2>
          <p>{t('termsOfService.privacyPolicyText')}</p>
          
          <h2>{t('termsOfService.thirdPartyLinks')}</h2>
          <p>{t('termsOfService.thirdPartyLinksText')}</p>
          
          <h2>{t('termsOfService.disclaimerWarranties')}</h2>
          <p>{t('termsOfService.disclaimerWarrantiesText')}</p>
          
          <h2>{t('termsOfService.limitationLiability')}</h2>
          <p>{t('termsOfService.limitationLiabilityText')}</p>
          
          <h2>{t('termsOfService.indemnification')}</h2>
          <p>{t('termsOfService.indemnificationText')}</p>
          
          <h2>{t('termsOfService.governingLaw')}</h2>
          <p>{t('termsOfService.governingLawText')}</p>
          
          <h2>{t('termsOfService.disputeResolution')}</h2>
          <p>{t('termsOfService.disputeResolutionText')}</p>
          
          <h2>{t('termsOfService.entireAgreement')}</h2>
          <p>{t('termsOfService.entireAgreementText')}</p>
          
          <h2>{t('termsOfService.waiver')}</h2>
          <p>{t('termsOfService.waiverText')}</p>
          
          <h2>{t('termsOfService.assignment')}</h2>
          <p>{t('termsOfService.assignmentText')}</p>
          
          <h2>{t('termsOfService.termination')}</h2>
          <p>{t('termsOfService.terminationText')}</p>
          
          <h2>{t('termsOfService.contact')}</h2>
          <p>{t('termsOfService.contactText')}</p>
          <p>{t('termsOfService.lastUpdated')}</p>
          
          <h2>{t('termsOfService.regulatoryCompliance')}</h2>
          <h3>{t('termsOfService.gdprCompliance')}</h3>
          <p>{t('termsOfService.gdprText')}</p>
          
          <h2>{t('termsOfService.dataCollection')}</h2>
          <p>{t('termsOfService.dataCollectionText')}</p>
          <p>{t('termsOfService.dataCollectionText2')}</p>
          
          <h2>{t('termsOfService.dataProcessing')}</h2>
          <p>{t('termsOfService.dataProcessingText')}</p>
          <p>{t('termsOfService.dataProcessingText2')}</p>
          
          <h2>{t('termsOfService.gdprRights')}</h2>
          <p>{t('termsOfService.gdprRightsText')}</p>
          <p>{t('termsOfService.gdprRightsText2')}</p>
          
          <h2>{t('termsOfService.internationalTransfers')}</h2>
          <p>{t('termsOfService.internationalTransfersText')}</p>
          <p>{t('termsOfService.internationalTransfersText2')}</p>
        </div>
      </div>
    </section>
  </main>
</Layout>
