---
import Layout from '../../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import LocationCard from '../../../components/LocationCard.astro';
import Breadcrumbs from '../../../components/Breadcrumbs.astro';
import locationsData from '../../../data/locations_pl.json';
import { getLangFromUrl, useTranslations } from '../../../i18n/utils';

// Get Polish rest area entries. IDs are like 'pl/some-area.md'.
const polishRestAreasOnly = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));

// Calculate unique highways for Poland using only these Polish entries
// The location_path check might be redundant if all 'pl/' ID entries are indeed for Poland locations.
// This page is for Polish display, so location_path should also align or be derived from Polish data.
const polandLocatedRestAreas = polishRestAreasOnly.filter(area => area.data.location_path.startsWith('polska/') || area.data.location_path.startsWith('poland/')); // Accommodate both path variants if data is mixed
const uniqueHighways = [...new Set(polandLocatedRestAreas.map(area => area.data.highway_tag))];

// Sort locations alphabetically by name
const sortedLocationsData = [...locationsData].sort((a, b) => a.name.localeCompare(b.name));

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Build breadcrumbs
const breadcrumbs = [
  { label: t('nav.home'), href: '/pl/' },
  { label: t('countries.PL'), href: '/pl/polska/' }
];

const pageTitle = t('meta.browsePolandTitle');
const pageDescription = t('meta.browsePolandDescription');
---

<Layout title={pageTitle} description={pageDescription}>
  <main>
    <div class="container-custom pt-8">
      <Breadcrumbs items={breadcrumbs} />

      <div class="mb-8">
        <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
          {t('meta.browsePolandTitle')}
        </h1>
        <p class="text-xl text-secondary-600 dark:text-secondary-300">
          {t('meta.browsePolandDescription')}
        </p>
      </div>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="card p-6 text-center">
          <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
            {locationsData.length}
          </div>
          <div class="text-secondary-600 dark:text-secondary-400">
            {t('stats.voivodeships')}
          </div>
        </div>

        <div class="card p-6 text-center">
          <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
            {uniqueHighways.length}
          </div>
          <div class="text-secondary-600 dark:text-secondary-400">
            {t('stats.highways')}
          </div>
        </div>

        <div class="card p-6 text-center">
          <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
            {polishRestAreasOnly.length}
          </div>
          <div class="text-secondary-600 dark:text-secondary-400">
            {t('stats.restAreas')}
          </div>
        </div>
      </div>

      <!-- Regions Grid -->
      <section>
        <h2 class="text-2xl font-semibold mb-6">{t('browse.byVoivodeship')}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedLocationsData.map(region => {
            // Use the filtered list of Polish rest areas for counting within regions
            const regionRestAreas = polishRestAreasOnly.filter(area =>
              area.data.location_path.includes(region.slug) 
            );

            return (
              <LocationCard
                location={region}
                href={`/pl/polska/${region.slug}/`}
                restAreaCount={regionRestAreas.length}
              />
            );
          })}
        </div>
      </section>

      <!-- Quick Actions -->
      <section class="mt-12 mb-16 text-center">
        <div class="bg-secondary-50 dark:bg-secondary-900 rounded-xl p-8">
          <h2 class="text-2xl font-semibold mb-4">{t('browse.lookingForSpecific')}</h2>
          <p class="text-secondary-600 dark:text-secondary-400 mb-6">
            {t('browse.browseAllOrSearch')}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/pl/rest-areas/" class="btn-primary">
              {t('browse.viewAllRestAreas')}
            </a>
            <a href="/pl/#search" class="btn-outline">
              {t('browse.searchByLocation')}
            </a>
          </div>
        </div>
      </section>
    </div>
  </main>
</Layout>
