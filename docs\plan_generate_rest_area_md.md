# Plan: Generate Rest Area Markdown Files from CSV

**Objective:** Create a Node.js script that reads `data_set.csv`, transforms each row into a markdown (`.md`) file, and places it into the `src/content/rest-areas/en/` directory. This script will ensure reusability for future data additions.

**Detailed Plan:**

1.  **Data Ingestion and Transformation (`scripts/generate-rest-area-md.js`):**
    *   The script will read `data_set.csv` line by line. The first line will be parsed as headers to establish column-to-data mapping.
    *   A function will be implemented to "slugify" `rest_area_id` values (e.g., "Gaj" -> "gaj") to create appropriate filenames (e.g., `mop-gaj.md`).
    *   Each row of the CSV will be processed into a structured JavaScript object, mapping CSV column names to meaningful keys.

2.  **Frontmatter Generation:**
    *   A dedicated function will generate the YAML frontmatter section of the markdown file. This function will take the processed data object for a single rest area as input.
    *   Each frontmatter field will be mapped from the CSV data:
        *   `title`: Derived from `rest_area_id` (e.g., "Gaj Rest Area").
        *   `publishedDate`: Will be set to the current date or `last_verified_date` from the CSV.
        *   `description_short`: Will be generated dynamically based on `mop_category`, `road_number`, and `location`.
        *   `address_line`: Constructed from `road_number`, `km_marker`, `location`, and `region`.
        *   `coordinates`: Pulled directly from `latitude` (`lat`) and `longitude` (`lon`).
        *   `work_hours`: Hardcoded to "00:00-24:00" for simplicity, or conditionally generated if relevant CSV data exists.
        *   `contact_info`, `rating`: Hardcoded with placeholder values or derived if data becomes available.
        *   `maps_url`: Dynamically generated using `latitude` and `longitude`.
        *   `amenities`: A nested object will be created, mapping CSV boolean/`yes`/`no` values (e.g., `toilets_available`, `gas_station_available`) to `true`/`false`.
        *   `featured_image`: A placeholder URL will be used, potentially using the `rest_area_id` for text on the image.
        *   `country_code`: From the `country` column (e.g., "PL").
        *   `location_path`: Generated hierarchically (e.g., "poland/mazowieckie/gaj") from `country`, `region`, and `location`.
        *   `highway_tag`: Mapped from `road_number`.
        *   Direct mappings for `administrator`, `mop_category`, `road_class`, `road_number`, `km_marker`, `travel_direction`, `parking_spaces_cars`, `parking_spaces_trucks` (from `parking_spaces_trucks_tir`), `parking_spaces_buses`, `security_personnel_on_site`, `last_verified_date`, `data_source_url`.

3.  **Content (Prose) Generation:**
    *   A separate function will generate the main body of the markdown content (the descriptive text below the frontmatter).
    *   This content will be structured into sections similar to `mop-gaj.md`:
        *   **General overview:** "Located on the [road_class] [road_number] highway in the [region] region, [location] offers essential services for travelers heading towards [travel_direction]..."
        *   **Facilities & Services:** Detail available amenities (e.g., toilets, gas station, restaurant) based on the `amenities` mapping.
        *   **Location & Access:** Describe the `km_marker` and `travel_direction`, and mention parking spaces (`parking_spaces_cars`, `parking_spaces_trucks_tir`).
        *   **Safety & Security:** Mention `area_lighting`, `cctv_video_surveillance`, and `security_personnel_on_site`.
    *   The language will be English for these generated files, matching the example `mop-gaj.md`.

**Flow Diagram:**

```mermaid
graph TD
    A[Start] --> B(Read data_set.csv)
    B --> C{For Each Row in CSV}
    C --> D{Extract & Transform Data}
    D --> E(Generate Frontmatter)
    D --> F(Generate Content Body)
    E --> G(Combine Frontmatter & Content)
    F --> G
    G --> H(Construct Filename)
    H --> I(Write .md File to src/content/rest-areas/en/)
    I --> J{More Rows?}
    J -- Yes --> C
    J -- No --> K[End]
```

**Reusable Aspects:**

*   **Scripted Process:** The entire generation is automated via a script, making it easy to rerun for new CSV entries or updated data.
*   **Modular Functions:** Separation of concerns into functions (data transformation, frontmatter generation, content generation) allows for easy modification of specific parts without affecting the whole.
*   **Configurable Mappings:** If CSV column names or output structure change, updates can be made within the script's mapping logic.