---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import RestAreaCard from '../../components/RestAreaCard.astro';
import LocationCard from '../../components/LocationCard.astro';
import OptimizedImage from '../../components/OptimizedImage.astro';
import locationsData from '../../data/locations_pl.json';
import { generateWebsiteStructuredData } from '../../utils/structuredData.ts';
import { getLangFromUrl, useTranslations } from '../../i18n/utils';
import Footer from '../../components/pl/Footer.astro';

// Get all rest areas for popular section using Content Layer API
const allRestAreas = await getCollection('rest-areas');
const popularRestAreas = allRestAreas.slice(0, 6); // Show first 6 as popular

// Sort locations alphabetically by name
const sortedLocationsData = [...locationsData].sort((a, b) => a.name.localeCompare(b.name));

// Extract unique highways/roads from rest areas data for search
const uniqueHighways = [...new Set(allRestAreas.map(area => area.data.highway_tag))].sort();

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Generate structured data for homepage
const websiteStructuredData = generateWebsiteStructuredData();
---

<script is:inline define:vars={{ sortedLocationsData, uniqueHighways }}>
  // Ensure data is always available
  function initializeSearchData() {
    window.locationsData = sortedLocationsData;
    window.uniqueHighways = uniqueHighways;
  }

  // Initialize immediately
  initializeSearchData();

  // Define Alpine.js component function
  function locationSearch() {
    return {
      activeTab: 'highway',
      selectedCountry: 'PL',
      selectedRegion: '',
      selectedHighway: '',
      searchTerm: '',
      regions: [],
      highways: [],

      init() {
        this.loadData();
        this.activeTab = 'highway';
        this.selectedCountry = 'PL';
      },

      loadData() {
        if (!window.locationsData || !window.uniqueHighways) {
          initializeSearchData();
        }
        this.regions = window.locationsData || [];
        this.highways = window.uniqueHighways || [];
      },

      switchTab(tab) {
        this.activeTab = tab;
      },

      search() {
        let url = '/pl/rest-areas/';
        const params = new URLSearchParams();

        if (this.activeTab === 'highway' && this.selectedHighway) {
          params.append('highway', this.selectedHighway);
        } else if (this.activeTab === 'location') {
          if (this.selectedCountry) params.append('country', this.selectedCountry);
          if (this.selectedRegion) params.append('region', this.selectedRegion);
        } else if (this.activeTab === 'text' && this.searchTerm.trim()) {
          params.append('q', this.searchTerm.trim());
        }

        if (params.toString()) {
          url += '?' + params.toString();
        }

        window.location.href = url;
      },

      clearSearch() {
        this.selectedCountry = 'PL';
        this.selectedRegion = '';
        this.selectedHighway = '';
        this.searchTerm = '';
      }
    }
  }

  // Make function globally available
  if (typeof window !== 'undefined') {
    window.locationSearch = locationSearch;
  }
</script>

<style>
  [x-cloak] { display: none !important; }
</style>

<Layout
  title={t('meta.defaultTitle')}
  description={t('meta.defaultDescription')}
  type="website"
  structuredData={websiteStructuredData}
  Footer={Footer}
  currentLang={currentLang}
>
  <main>
    <!-- Hero Section with Optimized Background -->
    <section class="section relative overflow-hidden">
      <!-- Optimized Background Image -->
      <div class="absolute inset-0 z-0">
        <OptimizedImage
          src="/images/home-background.jpg"
          alt="European highway rest area landscape"
          class="w-full h-full object-cover"
          priority={true}
          sizes="100vw"
          loading="eager"
        />
      </div>

      <!-- Gradient overlay for better text readability -->
      <div class="absolute inset-0 z-10 bg-gradient-to-br from-primary-50/90 to-accent-50/90 dark:from-secondary-900/90 dark:to-secondary-800/90"></div>

      <!-- Content -->
      <div class="container-custom relative z-20">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-6xl font-bold text-secondary-900 dark:text-white mb-6">
            {t('homepage.title')}
          </h1>
          <p class="text-xl md:text-2xl text-secondary-600 dark:text-secondary-300 mb-8">
            {t('homepage.subtitle')}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              @click="$dispatch('toggle-location-modal')"
              class="btn-primary"
            >
              {t('geolocation.findNearby')}
            </button>
            <a href="/pl/rest-areas/" class="btn-outline">
              {t('homepage.viewAllRestAreasBtn')}
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Enhanced Search Section -->
    <section class="section">
      <div class="container-custom">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-center mb-8">{t('homepage.searchTitle')}</h2>

          <div
            x-data="locationSearch()"
            class="bg-white dark:bg-secondary-900 rounded-xl p-6 shadow-lg"
          >
            <!-- Search Type Tabs -->
            <div class="flex flex-wrap gap-2 mb-6 border-b border-secondary-200 dark:border-secondary-700">
              <button
                @click="switchTab('highway')"
                :class="activeTab === 'highway' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-600 dark:text-secondary-400 hover:text-primary-600'"
                class="px-4 py-2 border-b-2 font-medium transition-colors"
              >
                {t('search.byHighway')}
              </button>
              <button
                @click="switchTab('location')"
                :class="activeTab === 'location' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-600 dark:text-secondary-400 hover:text-primary-600'"
                class="px-4 py-2 border-b-2 font-medium transition-colors"
              >
                {t('search.byLocation')}
              </button>
              <button
                @click="switchTab('text')"
                :class="activeTab === 'text' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-600 dark:text-secondary-400 hover:text-primary-600'"
                class="px-4 py-2 border-b-2 font-medium transition-colors"
              >
                {t('search.textSearch')}
              </button>
            </div>

            <!-- Highway Search -->
            <div x-show="activeTab === 'highway'" x-transition class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  {t('search.country')}
                </label>
                <select
                  x-model="selectedCountry"
                  class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
                >
                  <option value="PL">{t('countries.PL')}</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  {t('search.highway')}
                </label>
                <select
                  x-model="selectedHighway"
                  class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
                >
                  <option value="">{t('search.selectHighway')}</option>
                  <template x-for="highway in highways" :key="highway">
                    <option :value="highway" x-text="highway"></option>
                  </template>
                </select>
              </div>

              <div class="flex items-end md:col-span-2">
                <button
                  @click="search()"
                  :disabled="!selectedHighway"
                  class="w-full h-10 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('search.searchByHighway')}
                </button>
              </div>
            </div>

            <!-- Location Search -->
            <div x-show="activeTab === 'location'" x-transition class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  {t('search.country')}
                </label>
                <select
                  x-model="selectedCountry"
                  class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
                >
                  <option value="PL">{t('countries.PL')}</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  {t('search.region')} ({t('common.optional')})
                </label>
                <select
                  x-model="selectedRegion"
                  class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
                >
                  <option value="">{t('search.allRegions')}</option>
                  <template x-for="region in regions" :key="region.slug">
                    <option :value="region.slug" x-text="region.name"></option>
                  </template>
                </select>
              </div>

              <div class="flex items-end md:col-span-2">
                <button
                  @click="search()"
                  class="w-full h-10 btn-primary"
                >
                  {t('search.searchByLocation')}
                </button>
              </div>
            </div>

            <!-- Text Search -->
            <div x-show="activeTab === 'text'" x-transition class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">
                  {t('search.searchTerm')}
                </label>
                <input
                  type="text"
                  x-model="searchTerm"
                  placeholder={t('search.searchPlaceholder')}
                  class="w-full h-10 px-3 py-2 border border-secondary-300 dark:border-secondary-700 rounded-md bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100"
                  @keyup.enter="search()"
                />
              </div>

              <div class="flex items-end">
                <button
                  @click="search()"
                  :disabled="!searchTerm.trim()"
                  class="w-full h-10 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('common.search')}
                </button>
              </div>
            </div>

            <div class="mt-4 text-center">
              <button
                @click="clearSearch()"
                class="text-secondary-600 dark:text-secondary-400 hover:text-primary-600 text-sm"
              >
                {t('search.clearAllFilters')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Browse by Voivodeship Section -->
    <section class="section bg-secondary-50 dark:bg-secondary-900">
      <div class="container-custom">
        <h2 class="text-center mb-12">{t('homepage.browseByRegionTitle')}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedLocationsData.map(region => (
            <LocationCard
              location={region}
              href={`/pl/polska/${region.slug}/`}
              restAreaCount={allRestAreas.filter(area =>
                area.data.location_path.includes(region.slug)
              ).length}
            />
          ))}
        </div>
      </div>
    </section>

    <!-- Popular Rest Areas Section -->
    <section class="section">
      <div class="container-custom">
        <h2 class="text-center mb-12">{t('homepage.popularRestAreasTitle')}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularRestAreas.map(restArea => (
            <RestAreaCard restArea={restArea} />
          ))}
        </div>

        <div class="text-center mt-8">
          <a href="/pl/rest-areas/" class="btn-outline">
            {t('homepage.viewAllRestAreasBtn')}
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>
