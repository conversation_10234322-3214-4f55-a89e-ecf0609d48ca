---
/**
 * Tracking Manager Component
 * Centralized tracking management for GA4, Google Ads, Google AdSense, and other analytics
 */

import GoogleAnalytics from './GoogleAnalytics.astro';
import GoogleAds from './GoogleAds.astro';
import GoogleAdSense from './GoogleAdSense.astro';
import CookieConsent from './CookieConsent.astro';
import CookieConsentDebug from './CookieConsentDebug.astro';
import { getLangFromUrl } from '../i18n/utils';

export interface Props {
  enableGA4?: boolean;
  enableGoogleAds?: boolean;
  enableAdSense?: boolean;
  enableCookieConsent?: boolean;
  enableWebVitals?: boolean;
  enableEnhancedEcommerce?: boolean;
  customDimensions?: Record<string, string>;
  pageType?: string;
}

const {
  enableGA4 = true,
  enableGoogleAds = true,
  enableAdSense = true,
  enableCookieConsent = true,
  enableWebVitals = true,
  enableEnhancedEcommerce = true,
  customDimensions = {},
  pageType
} = Astro.props;

// Get current language and page information
const currentLang = getLangFromUrl(Astro.url);

// Determine page type if not provided
const getPageType = (url: URL) => {
  const pathname = url.pathname;
  if (pathname === '/' || pathname === '/pl/') return 'homepage';
  if (pathname.includes('/poland/') || pathname.includes('/polska/')) return 'location_browse';
  if (pathname.includes('/rest-areas/')) return 'rest_areas_listing';
  if (pathname.includes('/privacy-policy')) return 'privacy_policy';
  if (pathname.includes('/terms-of-service')) return 'terms_of_service';
  return 'other';
};

const currentPageType = pageType || getPageType(Astro.url);

// Enhanced custom dimensions with page context
const enhancedCustomDimensions = {
  page_type: currentPageType,
  language: currentLang,
  ...customDimensions
};

// Only load tracking in production or when explicitly enabled
const isProduction = import.meta.env.PROD;
const enableDevAnalytics = import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true';
const enableAnalytics = import.meta.env.PUBLIC_ENABLE_ANALYTICS !== 'false';
const shouldLoadTracking = enableAnalytics && (isProduction || enableDevAnalytics);
---

{shouldLoadTracking && (
  <>
    <!-- Google Analytics 4 -->
    {enableGA4 && (
      <GoogleAnalytics
        enableEnhancedEcommerce={enableEnhancedEcommerce}
        enableWebVitals={enableWebVitals}
        customDimensions={enhancedCustomDimensions}
      />
    )}

    <!-- Google Ads -->
    {enableGoogleAds && (
      <GoogleAds
        enableRemarketing={true}
        enableConversions={true}
      />
    )}

    <!-- Google AdSense -->
    {enableAdSense && (
      <GoogleAdSense
        enableAutoAds={true}
        enableManualAds={true}
      />
    )}

    <!-- Enhanced tracking integration script -->
    <script is:inline define:vars={{ currentPageType, currentLang }}>
      // Global tracking manager
      window.trackingManager = {
        // Track location search with both GA4 and Google Ads
        trackLocationSearch: function(searchData) {
          // GA4 tracking
          if (typeof window.trackLocationSearch === 'function') {
            window.trackLocationSearch(searchData);
          }
          
          // Google Ads remarketing
          if (typeof window.trackRemarketingSearch === 'function') {
            window.trackRemarketingSearch(searchData);
          }
          
          // Google Ads conversion
          if (typeof window.trackLocationSearchConversion === 'function') {
            window.trackLocationSearchConversion(searchData);
          }
        },

        // Track rest area view with enhanced data
        trackRestAreaView: function(restAreaData) {
          // GA4 enhanced ecommerce
          if (typeof window.trackRestAreaView === 'function') {
            window.trackRestAreaView(restAreaData);
          }
          
          // Google Ads remarketing
          if (typeof window.trackRemarketingRestAreaView === 'function') {
            window.trackRemarketingRestAreaView(restAreaData);
          }
          
          // Google Ads conversion
          if (typeof window.trackRestAreaViewConversion === 'function') {
            window.trackRestAreaViewConversion(restAreaData);
          }
        },

        // Track language switching
        trackLanguageSwitch: function(fromLang, toLang) {
          if (typeof window.trackLanguageSwitch === 'function') {
            window.trackLanguageSwitch(fromLang, toLang);
          }
        },

        // Track navigation with context
        trackNavigation: function(linkText, linkUrl, linkType = 'internal') {
          if (typeof window.trackNavigation === 'function') {
            window.trackNavigation(linkText, linkUrl, linkType);
          }
        },

        // Track dark mode toggle
        trackDarkModeToggle: function(mode) {
          if (typeof window.trackDarkModeToggle === 'function') {
            window.trackDarkModeToggle(mode);
          }
        },

        // Track high-value engagement
        trackEngagement: function(engagementData) {
          if (typeof window.trackEngagementConversion === 'function') {
            window.trackEngagementConversion({
              ...engagementData,
              page_type: currentPageType,
              language: currentLang
            });
          }
        },

        // Track form submissions
        trackFormSubmission: function(formData) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'form_submit', {
              event_category: 'Form Interaction',
              event_label: formData.formId || 'unknown_form',
              form_type: formData.type || 'unknown',
              page_type: currentPageType,
              language: currentLang
            });
          }
          
          // Track as conversion if it's a high-value form
          if (formData.isHighValue && typeof window.trackHighValueConversion === 'function') {
            window.trackHighValueConversion({
              type: 'form_submission',
              value: formData.formId || 'unknown_form',
              user_type: 'engaged_visitor'
            });
          }
        },

        // Track search results
        trackSearchResults: function(searchData, resultsCount) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'view_search_results', {
              search_term: searchData.term || '',
              search_type: searchData.type || 'location',
              results_count: resultsCount,
              page_type: currentPageType,
              language: currentLang
            });
          }
        },

        // Track user preferences
        trackUserPreference: function(preferenceType, preferenceValue) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'user_preference', {
              event_category: 'User Preference',
              event_label: `${preferenceType}_${preferenceValue}`,
              preference_type: preferenceType,
              preference_value: preferenceValue,
              page_type: currentPageType,
              language: currentLang
            });
          }
        }
      };

      // Auto-track page-specific events
      document.addEventListener('DOMContentLoaded', function() {
        // Track page type-specific events
        if (currentPageType === 'homepage') {
          // Track homepage engagement
          setTimeout(function() {
            window.trackingManager.trackEngagement({
              type: 'homepage_view',
              value: 'initial_load'
            });
          }, 2000);
        }

        // Track search form usage
        const searchForms = document.querySelectorAll('[data-search-form]');
        searchForms.forEach(function(form) {
          form.addEventListener('submit', function() {
            const formData = new FormData(form);
            const searchData = {
              type: formData.get('searchType') || 'location',
              term: formData.get('searchTerm') || '',
              country: formData.get('country') || 'PL',
              region: formData.get('region') || '',
              highway: formData.get('highway') || ''
            };
            
            window.trackingManager.trackLocationSearch(searchData);
          });
        });

        // Track language selector usage
        const languageSelectors = document.querySelectorAll('[data-language-selector]');
        languageSelectors.forEach(function(selector) {
          selector.addEventListener('change', function(e) {
            const newLang = e.target.value;
            window.trackingManager.trackLanguageSwitch(currentLang, newLang);
          });
        });

        // Track dark mode toggle
        const darkModeToggles = document.querySelectorAll('[data-dark-mode-toggle]');
        darkModeToggles.forEach(function(toggle) {
          toggle.addEventListener('click', function() {
            const isDark = document.documentElement.classList.contains('dark');
            const newMode = isDark ? 'light' : 'dark';
            window.trackingManager.trackDarkModeToggle(newMode);
          });
        });

        // Track rest area card clicks
        const restAreaCards = document.querySelectorAll('[data-rest-area-card]');
        restAreaCards.forEach(function(card) {
          card.addEventListener('click', function() {
            const restAreaData = {
              id: card.dataset.restAreaId,
              name: card.dataset.restAreaName,
              highway_tag: card.dataset.highway,
              region: card.dataset.region,
              direction: card.dataset.direction
            };
            
            window.trackingManager.trackRestAreaView(restAreaData);
          });
        });

        // Track external link clicks with enhanced data
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a');
          if (link && link.href && link.hostname !== window.location.hostname) {
            const linkData = {
              url: link.href,
              text: link.textContent?.trim() || link.getAttribute('aria-label') || 'Unknown',
              domain: link.hostname,
              page_type: currentPageType,
              language: currentLang
            };
            
            if (typeof gtag !== 'undefined') {
              gtag('event', 'click', {
                event_category: 'External Link',
                event_label: linkData.text,
                link_url: linkData.url,
                link_domain: linkData.domain,
                page_type: currentPageType,
                language: currentLang
              });
            }
          }
        });
      });

      // Track page visibility changes (for engagement metrics)
      document.addEventListener('visibilitychange', function() {
        if (typeof gtag !== 'undefined') {
          gtag('event', 'page_visibility_change', {
            event_category: 'User Engagement',
            event_label: document.hidden ? 'hidden' : 'visible',
            page_type: currentPageType,
            language: currentLang
          });
        }
      });
    </script>
  </>
)}

<!-- Cookie Consent Banner -->
{enableCookieConsent && (
  <CookieConsent enableGranularControls={true} />
)}

<!-- Debug Component (only in development) -->
{!import.meta.env.PROD && (
  <CookieConsentDebug showDebugInfo={true} />
)}
