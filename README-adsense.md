# Google AdSense Integration Guide

This guide explains how to implement Google AdSense for revenue generation on your Astro-based multilingual website with GDPR compliance and performance optimization.

## 🎯 What's Implemented

### AdSense Features
- **Auto Ads**: Automatic ad placement by Google's AI for optimal revenue
- **Manual Ad Units**: Precise control over ad placement and formatting
- **Responsive Design**: Ads adapt to all screen sizes automatically
- **Lazy Loading**: Performance optimization with intersection observer
- **GDPR Compliance**: Respects user consent preferences
- **Core Web Vitals**: Monitoring and optimization for page performance

### Ad Placement Components
- **HeaderAd**: Horizontal banner ads for header areas
- **SidebarAd**: Vertical ads for sidebar placement (with sticky option)
- **ContentAd**: Rectangle/square ads within content areas
- **FooterAd**: Horizontal banner ads for footer areas
- **AdUnit**: Base component for custom ad placements

## 🚀 Quick Start

### 1. Get AdSense Approval
1. Apply for Google AdSense at [adsense.google.com](https://adsense.google.com)
2. Wait for approval (can take days to weeks)
3. Get your Publisher ID (format: `ca-pub-1234567890123456`)

### 2. Configure Environment
Add to your `.env` file:
```env
# Google AdSense Configuration
PUBLIC_ADSENSE_PUBLISHER_ID=ca-pub-1234567890123456
PUBLIC_ADSENSE_AUTO_ADS=true
PUBLIC_ADSENSE_MANUAL_ADS=true
```

### 3. Create Ad Units (Optional for Manual Ads)
1. Go to your AdSense dashboard
2. Create new ad units
3. Get the ad slot IDs (format: `1234567890`)
4. Replace placeholder slots in components

### 4. Add Ads to Your Pages
```astro
---
import HeaderAd from '../components/ads/HeaderAd.astro';
import ContentAd from '../components/ads/ContentAd.astro';
---

<Layout>
  <!-- Header ad -->
  <HeaderAd adSlot="1234567890" />
  
  <main>
    <h1>Your Content</h1>
    <p>Some content...</p>
    
    <!-- Content ad -->
    <ContentAd 
      adSlot="2345678901" 
      format="rectangle"
      position="middle"
    />
    
    <p>More content...</p>
  </main>
</Layout>
```

## 📊 Ad Component Reference

### HeaderAd
```astro
<HeaderAd 
  adSlot="1234567890"     // Your ad unit ID
  showLabel={true}        // Show "Advertisement" label
  className="custom-class" // Additional CSS classes
/>
```

### SidebarAd
```astro
<SidebarAd 
  adSlot="2345678901"
  showLabel={true}
  sticky={true}           // Sticky positioning on desktop
  className="custom-class"
/>
```

### ContentAd
```astro
<ContentAd 
  adSlot="3456789012"
  format="rectangle"      // 'rectangle' | 'horizontal' | 'auto'
  position="middle"       // 'top' | 'middle' | 'bottom'
  showLabel={true}
  className="custom-class"
/>
```

### FooterAd
```astro
<FooterAd 
  adSlot="4567890123"
  showLabel={true}
  className="custom-class"
/>
```

### Custom AdUnit
```astro
<AdUnit 
  adSlot="5678901234"
  format="auto"           // 'auto' | 'rectangle' | 'vertical' | 'horizontal' | 'fluid'
  size="300x250"         // Custom size (optional)
  responsive={true}       // Responsive behavior
  lazy={true}            // Lazy loading
  placement="content"     // 'header' | 'sidebar' | 'content' | 'footer' | 'inline'
  label="Advertisement"   // Custom label
/>
```

## 🔒 Privacy & GDPR Compliance

### Automatic Consent Management
The AdSense implementation automatically:
- Respects cookie consent preferences
- Hides ads when advertising consent is denied
- Updates ad serving when consent changes
- Complies with GDPR requirements

### Consent Flow
1. User visits site → Cookie banner appears
2. User accepts/denies advertising cookies
3. AdSense respects the choice automatically
4. Ads load only with proper consent

### Manual Consent Control
```javascript
// Check current consent status
if (window.adSenseManager.consentGiven) {
  // Ads are enabled
}

// Update consent programmatically
window.adSenseManager.updateConsent({
  advertising: true  // or false
});
```

## ⚡ Performance Optimization

### Lazy Loading
Ads below the fold are lazy-loaded using Intersection Observer:
```astro
<AdUnit 
  adSlot="1234567890"
  lazy={true}  // Loads when coming into view
/>
```

### Core Web Vitals
The implementation monitors and optimizes:
- **LCP (Largest Contentful Paint)**: Delayed ad loading
- **CLS (Cumulative Layout Shift)**: Reserved space for ads
- **FID (First Input Delay)**: Non-blocking ad scripts

### Performance Monitoring
```javascript
// Track ad performance
gtag('event', 'adsense_ad_loaded', {
  event_category: 'AdSense',
  event_label: 'Ad loaded successfully'
});

// Monitor CLS impact
gtag('event', 'adsense_cls_impact', {
  event_category: 'AdSense',
  value: clsValue
});
```

## 🌍 Multilingual Support

AdSense works seamlessly with the multilingual setup:
- Same ads serve across all languages
- Consent preferences persist across language switches
- Ad performance tracked per language in Analytics

## 🛠️ Development & Testing

### Enable Development Mode
```env
PUBLIC_ENABLE_DEV_ANALYTICS=true
```

### Test Mode
AdSense automatically enables test mode in development:
```astro
<!-- Test ads in development -->
<script data-adbreak-test="on"></script>
```

### Debug Console
Monitor ad loading in browser console:
```
AdSense: Ads enabled with consent
AdSense: Ad unit loaded: ad-1234567890-xyz123
AdSense: Script loading error, ads may be blocked
```

## 📈 Revenue Optimization

### Auto Ads vs Manual Ads
- **Auto Ads**: Let Google optimize placement automatically
- **Manual Ads**: Control exact placement for better user experience
- **Hybrid**: Use both for maximum revenue

### Best Practices
1. **Above the fold**: Place one ad in header or early content
2. **Content integration**: Use content ads between paragraphs
3. **Sidebar**: Sticky sidebar ads for longer engagement
4. **Footer**: Catch users at page end
5. **Mobile optimization**: Ensure responsive design

### A/B Testing
Test different ad placements:
```astro
<!-- Version A: Rectangle ad -->
<ContentAd format="rectangle" />

<!-- Version B: Horizontal ad -->
<ContentAd format="horizontal" />
```

## 🔧 Troubleshooting

### Common Issues

**Ads not showing:**
- Check Publisher ID is correct
- Verify ad slots exist in AdSense dashboard
- Ensure user has given advertising consent
- Check browser ad blockers

**Performance issues:**
- Enable lazy loading for below-fold ads
- Monitor Core Web Vitals in Google Analytics
- Reduce number of ads per page if needed

**GDPR compliance:**
- Verify cookie consent banner is working
- Test with different consent choices
- Check privacy policy mentions AdSense

### Debug Commands
```javascript
// Check AdSense status
console.log(window.adSenseManager);

// Force refresh ads
window.adSenseManager.refreshManualAds();

// Check consent status
console.log(window.adSenseManager.consentGiven);
```

## 📞 Support Resources

- [Google AdSense Help Center](https://support.google.com/adsense)
- [AdSense Policies](https://support.google.com/adsense/answer/48182)
- [GDPR Compliance Guide](https://support.google.com/adsense/answer/9012903)
- [Core Web Vitals](https://web.dev/vitals/)

## 🎉 Next Steps

1. **Get AdSense approval** if you haven't already
2. **Add your Publisher ID** to the environment variables
3. **Create ad units** in your AdSense dashboard
4. **Replace placeholder ad slots** with real ones
5. **Test thoroughly** in development mode
6. **Monitor performance** in Google Analytics and AdSense
7. **Optimize placement** based on revenue data

The AdSense implementation is now ready to generate revenue from your website traffic! 🚀
