import { google } from 'googleapis';
import fs from 'fs/promises';
import path from 'path';

// Default rate limiting configuration
const DEFAULT_RATE_LIMIT = {
  requestsPerSecond: 10,
  maxRetries: 3,
  retryDelay: 1000, // 1 second
};

// Configure Google Sheets API client with enhanced authentication
export async function getGoogleSheetsClient() {
  try {
    let auth;
    
    // Try environment variable first (for GitHub Actions)
    if (process.env.GOOGLE_CREDENTIALS_JSON) {
      const credentials = JSON.parse(process.env.GOOGLE_CREDENTIALS_JSON);
      auth = new google.auth.GoogleAuth({
        credentials,
        scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
      });
    } else {
      // Fallback to local file (for development)
      const credentialsPath = path.join(process.cwd(), 'google-credentials.json');
      try {
        await fs.access(credentialsPath);
        auth = new google.auth.GoogleAuth({
          keyFile: credentialsPath,
          scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
        });
      } catch (error) {
        throw new Error('Google credentials not found. Please set GOOGLE_CREDENTIALS_JSON environment variable or create google-credentials.json file.');
      }
    }
    
    const client = await auth.getClient();
    return google.sheets({ version: 'v4', auth: client });
  } catch (error) {
    console.error('Failed to initialize Google Sheets client:', error);
    throw error;
  }
}

// Rate limiting utility
class RateLimiter {
  constructor(config = DEFAULT_RATE_LIMIT) {
    this.config = config;
    this.lastRequestTime = 0;
    this.requestCount = 0;
    this.resetTime = 0;
  }

  async waitIfNeeded() {
    const now = Date.now();
    
    // Reset counter every second
    if (now - this.resetTime >= 1000) {
      this.requestCount = 0;
      this.resetTime = now;
    }
    
    // Check if we need to wait
    if (this.requestCount >= this.config.requestsPerSecond) {
      const waitTime = 1000 - (now - this.resetTime);
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
        this.requestCount = 0;
        this.resetTime = Date.now();
      }
    }
    
    this.requestCount++;
  }
}

const rateLimiter = new RateLimiter();

// Enhanced fetch function with error handling and rate limiting
export async function fetchSheetData(
  spreadsheetId, 
  range,
  retries = DEFAULT_RATE_LIMIT.maxRetries
) {
  try {
    await rateLimiter.waitIfNeeded();
    
    const sheets = await getGoogleSheetsClient();
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range,
    });
    
    if (!response.data.values) {
      console.warn(`No data found in range ${range}`);
      return [];
    }
    
    return response.data.values;
  } catch (error) {
    console.error(`Error fetching sheet data (attempt ${DEFAULT_RATE_LIMIT.maxRetries - retries + 1}):`, error.message);
    
    if (retries > 0 && (error.code === 429 || error.code >= 500)) {
      console.log(`Retrying in ${DEFAULT_RATE_LIMIT.retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, DEFAULT_RATE_LIMIT.retryDelay));
      return fetchSheetData(spreadsheetId, range, retries - 1);
    }
    
    throw error;
  }
}

// Fetch multiple sheets with language support
export async function fetchMultipleSheets(configs) {
  const results = {};
  
  for (const config of configs) {
    try {
      console.log(`Fetching data from sheet: ${config.range} (${config.language || 'default'})`);
      const data = await fetchSheetData(config.spreadsheetId, config.range);
      const key = config.language || config.range;
      results[key] = data;
    } catch (error) {
      console.error(`Failed to fetch sheet ${config.range}:`, error.message);
      // Continue with other sheets even if one fails
      const key = config.language || config.range;
      results[key] = []; // Set empty array for failed sheets
    }
  }
  
  return results;
}

// Validate sheet structure
export async function validateSheetStructure(
  spreadsheetId, 
  range, 
  requiredColumns
) {
  try {
    const data = await fetchSheetData(spreadsheetId, range);
    
    if (data.length === 0) {
      return { isValid: false, missingColumns: requiredColumns, headers: [] };
    }
    
    const headers = data[0].map(header => header.toString().trim().toLowerCase());
    const normalizedRequired = requiredColumns.map(col => col.toLowerCase());
    const missingColumns = normalizedRequired.filter(col => !headers.includes(col));
    
    return {
      isValid: missingColumns.length === 0,
      missingColumns,
      headers: data[0]
    };
  } catch (error) {
    console.error('Error validating sheet structure:', error);
    return { isValid: false, missingColumns: requiredColumns, headers: [] };
  }
}
