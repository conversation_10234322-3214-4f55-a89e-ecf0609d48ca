import { defineCollection, z } from 'astro:content';
import { glob } from 'astro/loaders';

// Define the rest areas collection using the new Content Layer API
const restAreas = defineCollection({
  loader: glob({ pattern: '**/*.md', base: './src/content/rest-areas' }),
  schema: z.object({
    title: z.string(),
    slug: z.string().optional(),
    publishedDate: z.date(),
    description_short: z.string(),
    address_line: z.string(),
    coordinates: z.object({
      lat: z.number(),
      lon: z.number(),
    }),
    work_hours: z.string(), // JSON string format
    contact_info: z.string().optional(), // JSON string format
    maps_url: z.string().url().optional(),
    rating: z.number().min(0).max(5).optional(),
    locale: z.string().optional(), // Language locale (e.g., 'en', 'pl')
    amenities: z.object({
      toilets: z.boolean().default(false),
      wifi: z.boolean().default(false),
      fuel_station: z.boolean().default(false),
      restaurant: z.boolean().default(false),
      shop: z.boolean().default(false),
      playground: z.boolean().default(false),
      showers: z.boolean().default(false),
      car_wash: z.boolean().default(false),
      ev_charging: z.boolean().default(false),
      security: z.boolean().default(false),
      cctv: z.boolean().default(false),
      lighting: z.boolean().default(false),
      accommodation: z.boolean().default(false),
      fenced_area: z.boolean().default(false),
      vehicle_workshop: z.boolean().default(false),
      liquid_waste_disposal_rv: z.boolean().default(false),
      hydrogen_fueling: z.boolean().default(false),
      cng_fueling: z.boolean().default(false),
      lng_fueling: z.boolean().default(false),
      snow_removal_ramp_trucks: z.boolean().default(false),
    }),
    featured_image: z.string().default('https://placehold.co/600x400?text=Rest+Area'),
    country_code: z.string().length(2),
    location_path: z.string(), // e.g., "poland/podlaskie/bialystok"
    highway_tag: z.string(), // e.g., "A2", "S8"
    administrator: z.string().optional(),
    mop_category: z.string().optional(),
    road_class: z.string().optional(),
    road_number: z.string().optional(),
    km_marker: z.string().optional(),
    travel_direction: z.string().optional(),
    parking_spaces_cars: z.number().optional(),
    parking_spaces_trucks: z.number().optional(),
    parking_spaces_buses: z.number().optional(),
    parking_spaces_dangerous: z.number().optional(),
    toilets_accessible: z.boolean().default(false),
    ev_charger_details: z.string().optional(),
    security_personnel_on_site: z.boolean().default(false),
    last_verified_date: z.date().optional(),
    data_source_url: z.string().url().optional(),
    internal_notes: z.string().optional(),
  }),
});

export const collections = {
  'rest-areas': restAreas,
};
