#!/usr/bin/env node

/**
 * Content Extraction Script for Translation
 * 
 * This script extracts markdown content from English rest area files and saves them
 * as JSON files for manual translation. This eliminates language mixing issues by
 * separating content extraction from content generation.
 * 
 * Usage: node extract-content-for-translation.js [filename]
 * If no filename is provided, processes all English files.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directory paths
const englishMdDir = path.join(__dirname, '../src/content/rest-areas/en');
const englishJsonDir = path.join(__dirname, '../src/content/rest-areas/translation-json/en');
const consolidatedJsonFile = path.join(englishJsonDir, 'all-content-for-translation.json');

/**
 * Parse YAML frontmatter and extract specific translatable fields
 * @param {string} frontmatterText - YAML frontmatter content
 * @returns {Object} - Extracted translatable fields
 */
function parseTranslatableFrontmatter(frontmatterText) {
  const translatableFields = {};

  // Extract title field
  const titleMatch = frontmatterText.match(/^title:\s*"([^"]+)"/m);
  if (titleMatch) {
    translatableFields.title = titleMatch[1];
  }

  // Extract description_short field
  const descMatch = frontmatterText.match(/^description_short:\s*"([^"]+)"/m);
  if (descMatch) {
    translatableFields.description_short = descMatch[1];
  }

  // Extract administrator field
  const adminMatch = frontmatterText.match(/^administrator:\s*"([^"]+)"/m);
  if (adminMatch) {
    translatableFields.administrator = adminMatch[1];
  }

  // Extract mop_category field
  const categoryMatch = frontmatterText.match(/^mop_category:\s*"([^"]+)"/m);
  if (categoryMatch) {
    translatableFields.mop_category = categoryMatch[1];
  }

  return translatableFields;
}

/**
 * Parse markdown file and extract frontmatter and content
 * @param {string} fileContent - Raw file content
 * @returns {Object} - {frontmatter: Object, content: string}
 */
function parseMarkdownFile(fileContent) {
  // Normalize line endings
  const normalizedContent = fileContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Match frontmatter pattern: ---\n...content...\n---\n...markdown...
  const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
  const match = normalizedContent.match(frontmatterRegex);

  if (!match) {
    throw new Error('Invalid markdown file format - missing or malformed frontmatter');
  }

  const frontmatterText = match[1].trim();
  const contentText = match[2].trim();

  // Parse only translatable frontmatter fields
  const translatableFrontmatter = parseTranslatableFrontmatter(frontmatterText);

  return {
    frontmatter: translatableFrontmatter,
    content: contentText
  };
}

/**
 * Extract content from a single markdown file
 * @param {string} filename - Name of the markdown file
 * @returns {Object|null} - Extracted content or null if error
 */
function extractContentFromFile(filename) {
  const inputPath = path.join(englishMdDir, filename);

  // Check if input file exists
  if (!fs.existsSync(inputPath)) {
    console.error(`❌ Input file not found: ${filename}`);
    return null;
  }

  try {
    // Read and parse the markdown file
    const fileContent = fs.readFileSync(inputPath, 'utf-8');
    const { frontmatter, content } = parseMarkdownFile(fileContent);

    // Return simplified structure with only translatable content
    return {
      frontmatter: frontmatter,
      content: content
    };

  } catch (error) {
    console.error(`❌ Error processing ${filename}:`, error.message);
    return null;
  }
}

/**
 * Extract content from all markdown files and consolidate into single JSON
 * @param {string[]} filenames - Array of markdown filenames to process
 * @returns {Object} - Processing results
 */
function extractAllContentToConsolidatedJson(filenames) {
  const consolidatedData = {};
  let processed = 0;
  let extracted = 0;
  let errors = 0;

  console.log(`🔄 Processing ${filenames.length} files into consolidated JSON...\n`);

  for (const filename of filenames) {
    processed++;

    // Show progress for large batches
    if (processed % 50 === 0) {
      console.log(`📊 Progress: ${processed}/${filenames.length} files processed...`);
    }

    const extractedContent = extractContentFromFile(filename);

    if (extractedContent) {
      consolidatedData[filename] = extractedContent;
      extracted++;

      // Log individual file processing
      const contentLines = extractedContent.content.split('\n').length;
      const frontmatterFields = Object.keys(extractedContent.frontmatter).length;
      console.log(`✅ ${filename}: ${frontmatterFields} frontmatter fields, ${contentLines} lines`);
    } else {
      errors++;
    }
  }

  // Ensure output directory exists
  if (!fs.existsSync(englishJsonDir)) {
    fs.mkdirSync(englishJsonDir, { recursive: true });
  }

  // Write consolidated JSON file
  try {
    fs.writeFileSync(consolidatedJsonFile, JSON.stringify(consolidatedData, null, 2), 'utf-8');
    console.log(`\n📦 Consolidated JSON created: ${path.basename(consolidatedJsonFile)}`);
    console.log(`📁 Location: ${consolidatedJsonFile}`);
  } catch (error) {
    console.error(`❌ Error writing consolidated JSON:`, error.message);
    errors++;
  }

  return {
    processed,
    extracted,
    errors,
    consolidatedFile: consolidatedJsonFile
  };
}

/**
 * Get all markdown files from the English directory
 * @returns {string[]} - Array of markdown filenames
 */
function getEnglishMarkdownFiles() {
  if (!fs.existsSync(englishMdDir)) {
    throw new Error(`English markdown directory not found: ${englishMdDir}`);
  }
  
  return fs.readdirSync(englishMdDir)
    .filter(file => file.endsWith('.md'))
    .sort();
}

/**
 * Main execution function
 */
function main() {
  const args = process.argv.slice(2);

  console.log('📄 Content Extraction for Translation (Consolidated)');
  console.log('====================================================\n');

  try {
    if (args.length > 0) {
      // Process specific file and add to consolidated JSON
      const filename = args[0];
      console.log(`🔍 Processing specific file: ${filename}\n`);

      // Check if consolidated file exists and load it
      let consolidatedData = {};
      if (fs.existsSync(consolidatedJsonFile)) {
        try {
          const existingContent = fs.readFileSync(consolidatedJsonFile, 'utf-8');
          consolidatedData = JSON.parse(existingContent);
          console.log(`📂 Loaded existing consolidated file with ${Object.keys(consolidatedData).length} entries`);
        } catch (error) {
          console.log(`⚠️  Could not load existing consolidated file: ${error.message}`);
        }
      }

      const extractedContent = extractContentFromFile(filename);

      if (extractedContent) {
        consolidatedData[filename] = extractedContent;

        // Ensure output directory exists
        if (!fs.existsSync(englishJsonDir)) {
          fs.mkdirSync(englishJsonDir, { recursive: true });
        }

        // Write updated consolidated JSON
        fs.writeFileSync(consolidatedJsonFile, JSON.stringify(consolidatedData, null, 2), 'utf-8');

        const contentLines = extractedContent.content.split('\n').length;
        const frontmatterFields = Object.keys(extractedContent.frontmatter).length;

        console.log(`✅ Added to consolidated JSON: ${filename}`);
        console.log(`   Frontmatter fields: ${frontmatterFields}, Content: ${contentLines} lines`);
        console.log(`📦 Total entries in consolidated file: ${Object.keys(consolidatedData).length}`);
        console.log('\n🎉 Content extraction completed successfully!');
        console.log(`📁 Consolidated file: ${consolidatedJsonFile}`);
      } else {
        console.log('\n❌ Content extraction failed');
      }

    } else {
      // Process all files into consolidated JSON
      console.log('🔍 Scanning for English markdown files...\n');

      const files = getEnglishMarkdownFiles();

      if (files.length === 0) {
        console.log('📭 No English markdown files found to process.');
        return;
      }

      console.log(`📝 Found ${files.length} English files to process:`);
      files.slice(0, 5).forEach(file => console.log(`   - ${file}`));
      if (files.length > 5) {
        console.log(`   ... and ${files.length - 5} more files`);
      }
      console.log('');

      // Check if consolidated file already exists
      if (fs.existsSync(consolidatedJsonFile)) {
        console.log(`⚠️  Consolidated file already exists: ${path.basename(consolidatedJsonFile)}`);
        console.log('   This will overwrite the existing file.');
        console.log('   To add individual files, specify a filename as argument.\n');
      }

      // Extract all content to consolidated JSON
      const results = extractAllContentToConsolidatedJson(files);

      // Final summary
      console.log('\n📊 Extraction Summary:');
      console.log('======================');
      console.log(`📝 Total files processed: ${results.processed}`);
      console.log(`✅ Successfully extracted: ${results.extracted}`);
      console.log(`❌ Errors: ${results.errors}`);
      console.log(`📦 Consolidated file: ${path.basename(results.consolidatedFile)}`);
      console.log(`📁 Location: ${results.consolidatedFile}`);

      if (results.extracted > 0) {
        console.log('\n🎉 Content extraction completed successfully!');
        console.log('\n📋 Next Steps:');
        console.log('1. Review the consolidated JSON file');
        console.log('2. Create Polish translation: src/content/rest-areas/translation-json/pl/all-content-for-translation.json');
        console.log('3. Update generate-polish-from-json.js to work with consolidated format');
      }
    }

  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
