---
export interface BreadcrumbItem {
  label: string;
  href: string;
}

export interface Props {
  items: BreadcrumbItem[];
}

const { items } = Astro.props;
---

<nav aria-label="Breadcrumb" class="py-4">
  <ol class="flex items-center space-x-2 text-sm">
    {items.map((item, index) => (
      <li class="flex items-center">
        {index > 0 && (
          <svg 
            class="w-4 h-4 mx-2 text-secondary-400 dark:text-secondary-600" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fill-rule="evenodd" 
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" 
              clip-rule="evenodd"
            />
          </svg>
        )}
        {index === items.length - 1 ? (
          <span class="text-secondary-900 dark:text-secondary-100 font-medium">
            {item.label}
          </span>
        ) : (
          <a 
            href={item.href} 
            class="text-secondary-600 hover:text-primary-600 dark:text-secondary-400 dark:hover:text-primary-400 transition-colors"
          >
            {item.label}
          </a>
        )}
      </li>
    ))}
  </ol>
</nav>
