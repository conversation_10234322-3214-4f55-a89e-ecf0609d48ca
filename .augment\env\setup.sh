#!/bin/bash

# Setup script for Astro.js project
set -e

echo "🚀 Setting up Astro.js development environment..."

# Node.js is already installed, verify version
echo "📦 Node.js version: $(node --version)"
echo "📦 npm version: $(npm --version)"

# Install pnpm globally with sudo to avoid permission issues
sudo npm install -g pnpm

# Verify pnpm installation
echo "📦 pnpm version: $(pnpm --version)"

# Navigate to project directory
cd /mnt/persist/workspace

# Install project dependencies using pnpm
echo "📥 Installing project dependencies..."
pnpm install

# Verify key dependencies are installed
echo "📋 Verifying key dependencies..."
ls node_modules/@astrojs/ | head -3
ls node_modules/astro/ | head -3

# Add pnpm to PATH in user profile
echo 'export PATH="/usr/local/bin:$PATH"' >> $HOME/.profile

echo "✅ Setup completed successfully!"
echo "🧪 Ready to run tests..."