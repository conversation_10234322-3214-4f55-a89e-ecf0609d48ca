#!/usr/bin/env node

/**
 * Google Sheets Setup Validation Script
 * 
 * This script validates the Google Sheets integration setup and tests
 * the connection, authentication, and data structure.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const REQUIRED_COLUMNS = [
  'rest_area_id', 'title', 'latitude', 'longitude', 'road_class',
  'road_number', 'km_marker', 'region', 'location', 'country'
];

const OPTIONAL_COLUMNS = [
  'description_short', 'address_line', 'work_hours', 'contact_info',
  'rating', 'toilets_available', 'wifi', 'gas_station_available',
  'restaurant_bistro_available', 'shop', 'playground', 'showers_available',
  'car_wash_available', 'ev_charging_station', 'security_personnel_on_site',
  'cctv_video_surveillance', 'area_lighting', 'accommodation_available',
  'fenced_area', 'administrator', 'mop_category', 'travel_direction',
  'parking_spaces_cars', 'parking_spaces_trucks_tir', 'parking_spaces_buses',
  'parking_spaces_dangerous', 'toilets_accessible', 'ev_charger_details',
  'last_verified_date', 'data_source_url', 'internal_notes'
];

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTest(name, status, message, details = null) {
  results.tests.push({ name, status, message, details });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

function logTest(name, status, message, details = null) {
  const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${icon} ${name}: ${message}`);
  if (details) {
    console.log(`   ${details}`);
  }
  addTest(name, status, message, details);
}

async function validateEnvironment() {
  console.log('\n🔧 Validating Environment Configuration...\n');
  
  // Check for required environment variables
  const sheetsId = process.env.GOOGLE_SHEETS_ID;
  if (!sheetsId) {
    logTest('Environment Variables', 'FAIL', 'GOOGLE_SHEETS_ID not set');
    return false;
  } else {
    logTest('Environment Variables', 'PASS', 'GOOGLE_SHEETS_ID is configured');
  }
  
  // Check for credentials
  const credentialsJson = process.env.GOOGLE_CREDENTIALS_JSON;
  const credentialsFile = path.join(process.cwd(), 'google-credentials.json');
  
  let hasCredentials = false;
  
  if (credentialsJson) {
    try {
      JSON.parse(credentialsJson);
      logTest('Credentials (Environment)', 'PASS', 'GOOGLE_CREDENTIALS_JSON is valid JSON');
      hasCredentials = true;
    } catch (error) {
      logTest('Credentials (Environment)', 'FAIL', 'GOOGLE_CREDENTIALS_JSON is not valid JSON');
    }
  }
  
  if (!hasCredentials) {
    try {
      await fs.access(credentialsFile);
      const fileContent = await fs.readFile(credentialsFile, 'utf-8');
      JSON.parse(fileContent);
      logTest('Credentials (File)', 'PASS', 'google-credentials.json exists and is valid');
      hasCredentials = true;
    } catch (error) {
      logTest('Credentials (File)', 'FAIL', 'google-credentials.json not found or invalid');
    }
  }
  
  return hasCredentials;
}

async function validateGoogleSheetsConnection() {
  console.log('\n📊 Testing Google Sheets Connection...\n');
  
  try {
    // Dynamic import to handle potential missing dependencies
    const { getGoogleSheetsClient, fetchSheetData } = await import('../src/utils/googleSheetsClient.js');
    
    // Test client initialization
    try {
      const client = await getGoogleSheetsClient();
      logTest('Google Sheets Client', 'PASS', 'Successfully initialized Google Sheets client');
    } catch (error) {
      logTest('Google Sheets Client', 'FAIL', `Failed to initialize client: ${error.message}`);
      return false;
    }
    
    // Test basic connection
    const sheetsId = process.env.GOOGLE_SHEETS_ID;
    try {
      const testData = await fetchSheetData(sheetsId, 'A1:A1');
      logTest('Google Sheets Access', 'PASS', 'Successfully accessed Google Sheets');
      return true;
    } catch (error) {
      logTest('Google Sheets Access', 'FAIL', `Failed to access sheet: ${error.message}`);
      return false;
    }
    
  } catch (error) {
    logTest('Google Sheets Module', 'FAIL', `Failed to load Google Sheets module: ${error.message}`);
    return false;
  }
}

async function validateSheetStructure() {
  console.log('\n📋 Validating Sheet Structure...\n');
  
  try {
    const { fetchSheetData, validateSheetStructure } = await import('../src/utils/googleSheetsClient.js');
    const sheetsId = process.env.GOOGLE_SHEETS_ID;
    
    // Test English sheet
    try {
      const validation = await validateSheetStructure(sheetsId, 'English!A:AZ', REQUIRED_COLUMNS);
      
      if (validation.isValid) {
        logTest('English Sheet Structure', 'PASS', 'All required columns found');
      } else {
        logTest('English Sheet Structure', 'FAIL', 
          `Missing required columns: ${validation.missingColumns.join(', ')}`,
          `Available headers: ${validation.headers.join(', ')}`);
      }
      
      // Check for optional columns
      const optionalFound = OPTIONAL_COLUMNS.filter(col => 
        validation.headers.some(header => header.toLowerCase() === col.toLowerCase())
      );
      
      if (optionalFound.length > 0) {
        logTest('English Optional Columns', 'PASS', 
          `Found ${optionalFound.length}/${OPTIONAL_COLUMNS.length} optional columns`);
      } else {
        logTest('English Optional Columns', 'WARN', 'No optional columns found');
      }
      
    } catch (error) {
      logTest('English Sheet Access', 'FAIL', `Cannot access English sheet: ${error.message}`);
    }
    
    // Test Polish sheet
    try {
      const validation = await validateSheetStructure(sheetsId, 'Polish!A:AZ', REQUIRED_COLUMNS);
      
      if (validation.isValid) {
        logTest('Polish Sheet Structure', 'PASS', 'All required columns found');
      } else {
        logTest('Polish Sheet Structure', 'WARN', 
          `Missing required columns: ${validation.missingColumns.join(', ')}`,
          'Polish sheet is optional but recommended');
      }
      
    } catch (error) {
      logTest('Polish Sheet Access', 'WARN', 'Polish sheet not accessible (optional)');
    }
    
  } catch (error) {
    logTest('Sheet Structure Validation', 'FAIL', `Validation failed: ${error.message}`);
  }
}

async function validateDataSample() {
  console.log('\n🔍 Validating Data Sample...\n');
  
  try {
    const { fetchSheetData } = await import('../src/utils/googleSheetsClient.js');
    const sheetsId = process.env.GOOGLE_SHEETS_ID;
    
    // Fetch a small sample of data
    const sampleData = await fetchSheetData(sheetsId, 'English!A1:AZ10');
    
    if (sampleData.length < 2) {
      logTest('Data Sample', 'WARN', 'Sheet appears to be empty or has only headers');
      return;
    }
    
    const headers = sampleData[0].map(h => h.toString().toLowerCase());
    const dataRows = sampleData.slice(1);
    
    logTest('Data Sample', 'PASS', `Found ${dataRows.length} sample data rows`);
    
    // Validate sample data
    let validRows = 0;
    let invalidRows = 0;
    
    for (const [index, row] of dataRows.entries()) {
      const rowData = {};
      headers.forEach((header, colIndex) => {
        rowData[header] = row[colIndex] || '';
      });
      
      // Check required fields
      const missingRequired = REQUIRED_COLUMNS.filter(col => 
        !rowData[col.toLowerCase()] || rowData[col.toLowerCase()].trim() === ''
      );
      
      if (missingRequired.length === 0) {
        validRows++;
      } else {
        invalidRows++;
        if (invalidRows <= 3) { // Only show first 3 invalid rows
          logTest(`Data Row ${index + 2}`, 'WARN', 
            `Missing required fields: ${missingRequired.join(', ')}`);
        }
      }
    }
    
    if (validRows > 0) {
      logTest('Data Validation', 'PASS', 
        `${validRows}/${dataRows.length} rows have all required fields`);
    } else {
      logTest('Data Validation', 'FAIL', 'No valid data rows found');
    }
    
  } catch (error) {
    logTest('Data Sample Validation', 'FAIL', `Failed to validate data: ${error.message}`);
  }
}

async function validateDependencies() {
  console.log('\n📦 Validating Dependencies...\n');
  
  // Check for required Node.js modules
  const requiredModules = ['googleapis'];
  
  for (const module of requiredModules) {
    try {
      await import(module);
      logTest(`Module: ${module}`, 'PASS', 'Module is available');
    } catch (error) {
      logTest(`Module: ${module}`, 'FAIL', 
        `Module not found. Run: npm install ${module}`);
    }
  }
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    logTest('Node.js Version', 'PASS', `Node.js ${nodeVersion} is supported`);
  } else {
    logTest('Node.js Version', 'WARN', 
      `Node.js ${nodeVersion} may not be fully supported. Recommended: 18+`);
  }
}

async function validateFileStructure() {
  console.log('\n📁 Validating File Structure...\n');
  
  const requiredFiles = [
    'src/utils/googleSheetsClient.ts',
    'scripts/export-from-sheets.js',
    'scripts/generate-from-json.js',
    '.github/workflows/sync-content.yml'
  ];
  
  for (const file of requiredFiles) {
    try {
      await fs.access(file);
      logTest(`File: ${file}`, 'PASS', 'File exists');
    } catch (error) {
      logTest(`File: ${file}`, 'FAIL', 'File not found');
    }
  }
  
  // Check for output directories
  const outputDirs = [
    'src/content/rest-areas/en',
    'src/content/rest-areas/pl'
  ];
  
  for (const dir of outputDirs) {
    try {
      await fs.access(dir);
      logTest(`Directory: ${dir}`, 'PASS', 'Directory exists');
    } catch (error) {
      logTest(`Directory: ${dir}`, 'WARN', 'Directory will be created when needed');
    }
  }
}

function printSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Warnings: ${results.warnings}`);
  console.log(`📋 Total Tests: ${results.tests.length}`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All critical validations passed! The Google Sheets sync system should work correctly.');
    
    if (results.warnings > 0) {
      console.log('\n⚠️  Some warnings were found. Review them to ensure optimal functionality.');
    }
    
    console.log('\n🚀 Next steps:');
    console.log('   1. Test the export script: node scripts/export-from-sheets.js');
    console.log('   2. Test markdown generation: node scripts/generate-from-json.js');
    console.log('   3. Run Astro build: npm run build');
    
  } else {
    console.log('\n❌ Some critical validations failed. Please fix the issues before proceeding.');
    
    console.log('\n🔧 Failed tests:');
    results.tests
      .filter(test => test.status === 'FAIL')
      .forEach(test => console.log(`   - ${test.name}: ${test.message}`));
  }
  
  console.log('\n📖 For detailed setup instructions, see: docs/google-sheets-sync.md');
}

async function main() {
  console.log('🔍 Google Sheets Integration Validation');
  console.log('=====================================');
  
  try {
    await validateDependencies();
    await validateFileStructure();
    await validateEnvironment();
    await validateGoogleSheetsConnection();
    await validateSheetStructure();
    await validateDataSample();
    
    printSummary();
    
    // Exit with appropriate code
    process.exit(results.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('\n💥 Validation failed with unexpected error:', error.message);
    process.exit(1);
  }
}

// Run validation if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as validateSheetsSetup };
