<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cookie Consent</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #b91c1c;
        }
        .success {
            background: #10b981;
        }
        .success:hover {
            background: #059669;
        }
        .info {
            background: #3b82f6;
        }
        .info:hover {
            background: #2563eb;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            background: #f3f4f6;
            border-left: 4px solid #6b7280;
        }
        .code {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍪 Cookie Consent Troubleshooting</h1>
        
        <p>This page helps you troubleshoot cookie consent banner issues by clearing saved preferences and providing debugging information.</p>
        
        <div class="status" id="status">
            <strong>Current Status:</strong> Checking...
        </div>
        
        <h2>Actions</h2>
        <button onclick="clearConsent()">Clear Cookie Consent</button>
        <button onclick="checkStatus()" class="info">Check Status</button>
        <button onclick="goToHomepage()" class="success">Go to Homepage</button>
        <button onclick="goToTestPage()" class="info">Go to Test Page</button>
        
        <h2>Manual Commands</h2>
        <p>You can also run these commands in your browser console:</p>
        
        <div class="code">
// Clear all cookie consent data
localStorage.removeItem('cookie-consent');
localStorage.removeItem('cookie-consent-date');

// Check current consent status
console.log('Consent:', localStorage.getItem('cookie-consent'));
console.log('Date:', localStorage.getItem('cookie-consent-date'));

// Force show banner (if it exists)
const banner = document.getElementById('cookie-consent-banner');
if (banner && banner._x_dataStack) {
    banner._x_dataStack[0].consentGiven = false;
}
        </div>
        
        <h2>Environment Check</h2>
        <div id="env-check">
            <p>Checking environment variables...</p>
        </div>
        
        <h2>Troubleshooting Steps</h2>
        <ol>
            <li><strong>Clear Consent:</strong> Click "Clear Cookie Consent" button above</li>
            <li><strong>Restart Server:</strong> Stop and restart your development server</li>
            <li><strong>Check Environment:</strong> Ensure <code>PUBLIC_ENABLE_DEV_ANALYTICS=true</code> in .env</li>
            <li><strong>Clear Cache:</strong> Hard refresh your browser (Ctrl+Shift+R or Cmd+Shift+R)</li>
            <li><strong>Check Console:</strong> Open browser developer tools and look for errors</li>
            <li><strong>Test Page:</strong> Visit the test page for detailed debugging</li>
        </ol>
        
        <h2>Expected Behavior</h2>
        <p>After clearing consent and refreshing the page, you should see:</p>
        <ul>
            <li>Cookie consent banner at the bottom of the page</li>
            <li>Debug messages in browser console (in development mode)</li>
            <li>Banner should have buttons: "Accept Essential", "Accept All", and optionally "Customize"</li>
        </ul>
        
        <div class="status" style="border-left-color: #f59e0b; background: #fef3c7;">
            <strong>Note:</strong> If you're in development mode, make sure <code>PUBLIC_ENABLE_DEV_ANALYTICS=true</code> is set in your .env file, otherwise the banner will only show in production.
        </div>
    </div>

    <script>
        function clearConsent() {
            try {
                localStorage.removeItem('cookie-consent');
                localStorage.removeItem('cookie-consent-date');
                
                // Also clear any other related items
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.includes('consent') || key.includes('cookie')) {
                        localStorage.removeItem(key);
                    }
                });
                
                document.getElementById('status').innerHTML = 
                    '<strong>✅ Success:</strong> Cookie consent data cleared! Refresh the page to see the banner.';
                document.getElementById('status').style.borderLeftColor = '#10b981';
                document.getElementById('status').style.background = '#d1fae5';
                
                console.log('🍪 Cookie consent cleared successfully');
                
                // Auto-refresh after 2 seconds
                setTimeout(() => {
                    if (confirm('Consent cleared! Refresh the page now to see the banner?')) {
                        window.location.reload();
                    }
                }, 1000);
                
            } catch (error) {
                document.getElementById('status').innerHTML = 
                    '<strong>❌ Error:</strong> ' + error.message;
                document.getElementById('status').style.borderLeftColor = '#dc2626';
                document.getElementById('status').style.background = '#fee2e2';
            }
        }
        
        function checkStatus() {
            const consent = localStorage.getItem('cookie-consent');
            const consentDate = localStorage.getItem('cookie-consent-date');
            const banner = document.getElementById('cookie-consent-banner');
            
            let status = '<strong>Current Status:</strong><br>';
            
            if (consent) {
                const preferences = JSON.parse(consent);
                status += `• Consent: <span style="color: #dc2626;">SAVED</span> (${consentDate ? new Date(consentDate).toLocaleDateString() : 'no date'})<br>`;
                status += `• Analytics: ${preferences.analytics ? '✅ Allowed' : '❌ Denied'}<br>`;
                status += `• Advertising: ${preferences.advertising ? '✅ Allowed' : '❌ Denied'}<br>`;
                status += `• <em>Banner should be hidden because consent was already given</em>`;
            } else {
                status += '• Consent: <span style="color: #10b981;">NOT SAVED</span><br>';
                status += '• <em>Banner should be visible (if environment is correct)</em>';
            }
            
            document.getElementById('status').innerHTML = status;
        }
        
        function goToHomepage() {
            window.location.href = '/';
        }
        
        function goToTestPage() {
            window.location.href = '/test-cookie-consent';
        }
        
        // Check environment on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            
            // Try to detect environment (this won't work perfectly but gives some info)
            const envCheck = document.getElementById('env-check');
            let envInfo = '<p><strong>Environment Detection:</strong><br>';
            envInfo += `• Current URL: ${window.location.href}<br>`;
            envInfo += `• Protocol: ${window.location.protocol}<br>`;
            envInfo += `• Host: ${window.location.host}<br>`;
            
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                envInfo += '• Mode: <span style="color: #f59e0b;">Development (likely)</span><br>';
                envInfo += '• <em>Make sure PUBLIC_ENABLE_DEV_ANALYTICS=true in .env</em>';
            } else {
                envInfo += '• Mode: <span style="color: #10b981;">Production (likely)</span><br>';
                envInfo += '• <em>Banner should show if PUBLIC_ENABLE_COOKIE_CONSENT=true</em>';
            }
            
            envInfo += '</p>';
            envCheck.innerHTML = envInfo;
        });
    </script>
</body>
</html>
