# Google Maps Integration for Rest Area Pages

## Overview

This document describes the Google Maps integration implemented for the rest area pages in the Astro Sassify Template. The integration replaces the placeholder images with interactive Google Maps that show the exact location of each rest area using coordinates from the markdown frontmatter.

## Implementation Details

### GoogleMap Component

**Location**: `src/components/GoogleMap.astro`

The GoogleMap component is a reusable Astro component that:
- Takes coordinates (lat, lon), title, and address as props
- Shows a clickable placeholder initially to improve page load performance
- Loads an embedded Google Maps iframe when clicked
- Includes fallback handling for map loading errors
- Provides a direct link to open the location in Google Maps

### Key Features

1. **Performance Optimized**: Maps are loaded on-demand when users click the placeholder
2. **Responsive Design**: Adapts to different screen sizes with proper styling
3. **Accessibility**: Includes proper ARIA labels and semantic HTML
4. **Error Handling**: Shows fallback content if the map fails to load
5. **Dark Mode Support**: Automatically switches between light and dark map themes
6. **Dynamic Theme Switching**: Detects theme changes in real-time and updates the map accordingly

### Usage

The component is used in both rest area page templates:
- `src/pages/rest-areas/[slug].astro`
- `src/pages/pl/[...restAreaSlug].astro`

```astro
<GoogleMap
  lat={restArea.data.coordinates.lat}
  lon={restArea.data.coordinates.lon}
  title={restArea.data.title}
  address={restArea.data.address_line}
  className="w-full h-64 lg:h-80 rounded-xl shadow-lg"
/>
```

### Data Source

The coordinates are sourced from the frontmatter of each rest area markdown file:

```yaml
coordinates:
  lat: 53.135688
  lon: 22.750355
```

## Technical Implementation

### Map Embedding

The component uses Google Maps' embed URL format that works without requiring an API key:

```
https://maps.google.com/maps?q={lat},{lon}&t=&z=15&ie=UTF8&iwloc=&output=embed
```

**Dark Mode Implementation**: Since Google Maps embed API doesn't support custom styling without an API key, the component uses CSS filters to create a dark mode effect:

```css
/* Dark mode filter applied to iframe */
filter: invert(0.9) hue-rotate(180deg) brightness(0.9) contrast(1.1);
```

This filter combination:
- `invert(0.9)`: Inverts colors while keeping some original brightness
- `hue-rotate(180deg)`: Rotates hues to maintain color relationships
- `brightness(0.9)`: Slightly reduces brightness for better dark mode appearance
- `contrast(1.1)`: Increases contrast for better readability

### Interactive Features

- **Click-to-load**: Improves initial page load performance
- **Loading states**: Shows spinner while map loads
- **Error handling**: Graceful fallback if map fails to load
- **External link**: Direct access to Google Maps for full functionality

### Alpine.js Integration

The component uses Alpine.js for:
- State management (showMap, mapLoaded, mapError, isDarkMode)
- Click handlers for user interactions
- Smooth transitions between states
- Dynamic theme detection and CSS filter application

### Dark Mode Implementation

The dark mode functionality works through several mechanisms:

1. **Initial Detection**: On component initialization, checks if `document.documentElement` has the `dark` class
2. **Dynamic Switching**: Uses a `MutationObserver` to watch for class changes on the HTML element
3. **CSS Filter Application**: Dynamically applies CSS filters to the iframe based on current theme
4. **Real-time Updates**: When the user switches themes, the map automatically updates without requiring a reload

```javascript
// Theme detection and watching
x-init="
  isDarkMode = document.documentElement.classList.contains('dark');
  const observer = new MutationObserver(() => {
    isDarkMode = document.documentElement.classList.contains('dark');
  });
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
"

// Dynamic CSS filter application
:style="isDarkMode ? 'border:0; filter: invert(0.9) hue-rotate(180deg) brightness(0.9) contrast(1.1);' : 'border:0;'"
```

## Browser Compatibility

The implementation works across all modern browsers and includes:
- Progressive enhancement
- Graceful degradation for older browsers
- Mobile-responsive design

## Future Enhancements

### Potential Improvements

1. **Google Maps JavaScript API**: For more advanced features like custom markers, styling, and interactivity
2. **API Key Integration**: Environment variable support for Google Maps API key
3. **Custom Markers**: Rest area-specific icons and information windows
4. **Clustering**: For pages showing multiple rest areas
5. **Directions Integration**: Direct links to get directions to the rest area

### API Key Setup (Optional)

To use advanced Google Maps features:

1. Get a Google Maps API key from Google Cloud Console
2. Add it to environment variables
3. Update the component to use the JavaScript API instead of embed URL

```javascript
// Example for future implementation
function initGoogleMap(lat, lon, title) {
  const map = new google.maps.Map(document.getElementById('map'), {
    center: { lat: lat, lng: lon },
    zoom: 15
  });

  new google.maps.Marker({
    position: { lat: lat, lng: lon },
    map: map,
    title: title
  });
}
```

## Testing

The integration has been tested with:
- All existing rest area pages
- Both page template formats (`/rest-areas/[slug]` and `/pl/[...restAreaSlug]`)
- Different coordinate sets
- Mobile and desktop viewports
- Light and dark theme switching
- Real-time theme changes (switching between light/dark while map is loaded)
- Map loading states and error handling

## Files Modified

1. **Created**: `src/components/GoogleMap.astro` - Main component
2. **Modified**: `src/pages/rest-areas/[slug].astro` - Added GoogleMap import and usage
3. **Modified**: `src/pages/pl/[...restAreaSlug].astro` - Added GoogleMap import and usage

## Coordinates Used

The implementation correctly uses the coordinates object from the markdown frontmatter:
- **Malankowo**: lat: 53.341425, lon: 18.708027
- **Jeżewo**: lat: 53.135688, lon: 22.750355
- **Wieszowa**: lat: 50.371763, lon: 18.741567

All coordinates are properly displayed on the maps and link correctly to Google Maps for external navigation.
