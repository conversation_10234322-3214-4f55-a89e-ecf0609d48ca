---
/**
 * Sidebar Ad Component
 * Optimized ad placement for sidebar areas
 */

import AdUnit from '../AdUnit.astro';

export interface Props {
  adSlot?: string;
  showLabel?: boolean;
  sticky?: boolean;
  className?: string;
}

const {
  adSlot = '2345678901', // Default placeholder - replace with actual ad slot
  showLabel = true,
  sticky = false,
  className = ''
} = Astro.props;
---

<div class={`sidebar-ad-container ${sticky ? 'sticky-ad' : ''} ${className}`}>
  <AdUnit
    adSlot={adSlot}
    format="vertical"
    placement="sidebar"
    responsive={true}
    lazy={true}
    className="sidebar-ad"
    label={showLabel ? "Advertisement" : undefined}
  />
</div>

<style>
  .sidebar-ad-container {
    /* Sidebar-specific container */
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
  
  .sticky-ad {
    position: sticky;
    top: 2rem;
    z-index: 10;
  }
  
  .sidebar-ad {
    /* Sidebar ad styling */
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .dark .sidebar-ad {
    background: #1f2937;
    border-color: #374151;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
  }
  
  @media (max-width: 1024px) {
    .sidebar-ad-container {
      max-width: 100%;
    }
    
    .sticky-ad {
      position: static;
    }
  }
  
  @media (max-width: 768px) {
    .sidebar-ad {
      padding: 0.75rem;
      margin: 1rem 0;
    }
  }
</style>
