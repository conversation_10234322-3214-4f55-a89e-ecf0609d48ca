#!/usr/bin/env node

/**
 * Polish Translation Generator for Rest Area Content
 *
 * This script reads existing English rest area content files and creates
 * Polish translation templates with translated structure and metadata.
 *
 * Usage: node scripts/create-pl-translations.js [filename]
 *
 * If no filename is provided, it will process all English files that don't
 * already have Polish translations.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Translation mappings for common terms
const translations = {
  // Amenity categories
  'Available Amenities': 'Dostępne Udogodnienia',
  'Facilities & Services': 'Udogodnienia i Usługi',
  'Location & Access': 'Lokalizacja i Dostęp',
  'Safety & Security': 'Bezpieczeństwo i Ochrona',

  // Common phrases
  'Rest Area': 'Miejsce Odpoczynku',
  'Highway Rest Area': 'Miejsce Odpoczynku na Autostradzie',
  'Your Highway Stop': 'Twój Przystanek na Autostradzie',
  'Private parking area': 'Prywatny parking',
  'with basic amenities': 'z podstawowymi udogodnieniami',
  'including toilets': 'w tym toaletami',
  'gas station': 'stacją paliw',
  'restaurant facilities': 'restauracją',
  'fuel station': 'stacja paliw',
  'clean toilet facilities': 'czyste toalety',
  'serving local and international cuisine': 'serwującą kuchnię lokalną i międzynarodową',
  'operates 24/7': 'działa całodobowo',
  'ideal stop for long-distance travelers': 'idealny przystanek dla podróżnych dalekobieżnych',
  'at any time of day or night': 'o każdej porze dnia i nocy',
  'easily accessible': 'łatwo dostępne',
  'adequate parking': 'odpowiednią liczbę miejsc parkingowych',
  'designated spaces': 'wydzielone strefy',
  'different vehicle types': 'różnych typów pojazdów',
  'well-lit': 'dobrze oświetlony',
  'CCTV surveillance': 'monitoring CCTV',
  'enhanced security': 'zwiększenia bezpieczeństwa',
  'no dedicated security personnel': 'nie ma dedykowanego personelu ochrony',
  '24-hour operation': 'całodobowa praca',
  'good lighting': 'dobre oświetlenie',
  'safe environment': 'bezpieczne środowisko',

  // Administrator types
  'Private': 'Prywatny',
  'Public': 'Publiczny',
  'State': 'Państwowy',

  // Category types
  'Private Parking': 'Parking Prywatny',
  'Service Area': 'Miejsce Obsługi Podróżnych',
  'Rest Stop': 'Miejsce Odpoczynku',

  // Regions (basic mapping)
  'Podlaskie': 'Podlaskie',
  'Mazowieckie': 'Mazowieckie',
  'Śląskie': 'Śląskie',
  'Wielkopolskie': 'Wielkopolskie'
};

// Function to translate text using the mapping
function translateText(text) {
  let translated = text;

  // Apply translations
  Object.entries(translations).forEach(([english, polish]) => {
    const regex = new RegExp(english, 'gi');
    translated = translated.replace(regex, polish);
  });

  return translated;
}

// Function to generate Polish frontmatter
function generatePolishFrontmatter(englishFrontmatter) {
  const polishFrontmatter = { ...englishFrontmatter };

  // Translate title
  if (polishFrontmatter.title) {
    polishFrontmatter.title = translateText(polishFrontmatter.title);
  }

  // Translate description
  if (polishFrontmatter.description_short) {
    polishFrontmatter.description_short = translateText(polishFrontmatter.description_short);
  }

  // Translate administrator
  if (polishFrontmatter.administrator) {
    polishFrontmatter.administrator = translateText(polishFrontmatter.administrator);
  }

  // Translate category
  if (polishFrontmatter.mop_category) {
    polishFrontmatter.mop_category = translateText(polishFrontmatter.mop_category);
  }

  // Update featured image text
  if (polishFrontmatter.featured_image && polishFrontmatter.featured_image.includes('text=')) {
    polishFrontmatter.featured_image = polishFrontmatter.featured_image.replace(
      /text=([^&]+)/,
      (match, text) => `text=${encodeURIComponent(translateText(decodeURIComponent(text)))}`
    );
  }

  // Add locale
  polishFrontmatter.locale = 'pl';

  return polishFrontmatter;
}

// Function to generate Polish content
function generatePolishContent(englishContent) {
  let polishContent = translateText(englishContent);

  // Additional specific translations for content
  polishContent = polishContent
    .replace(/Located on the ([A-Z0-9]+) highway/gi, 'Położone na drodze ekspresowej $1')
    .replace(/in the scenic ([^,]+) region/gi, 'w malowniczym województwie $1')
    .replace(/heading towards ([^.]+)/gi, 'zmierzających w kierunku $1')
    .replace(/This privately operated facility/gi, 'Ten prywatnie zarządzany obiekt')
    .replace(/provides a convenient stopping point/gi, 'stanowi wygodny punkt postojowy')
    .replace(/to ensure a comfortable journey/gi, 'zapewniającymi komfortową podróż')
    .replace(/Situated at kilometer marker/gi, 'Usytuowane przy słupku kilometrowym')
    .replace(/traveling in the ([^.]+) direction/gi, 'podróżujących w kierunku $1')
    .replace(/The facility provides/gi, 'Obiekt zapewnia')
    .replace(/for both cars and trucks/gi, 'zarówno dla samochodów osobowych, jak i ciężarowych')
    .replace(/The area is/gi, 'Teren jest')
    .replace(/equipped with/gi, 'wyposażony w')
    .replace(/for enhanced security/gi, 'dla zwiększenia bezpieczeństwa')
    .replace(/While there's/gi, 'Choć')
    .replace(/on-site/gi, 'na miejscu')
    .replace(/provide a safe environment for travelers/gi, 'zapewniają bezpieczne środowisko dla podróżnych');

  return polishContent;
}

// Function to parse frontmatter and content (simplified approach)
function parseMdFile(content) {
  // Normalize line endings
  const normalizedContent = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
  const match = normalizedContent.match(frontmatterRegex);

  if (!match) {
    throw new Error('Invalid markdown file format');
  }

  const frontmatterText = match[1];
  const contentText = match[2];

  return { frontmatterText, content: contentText };
}

// Function to process frontmatter text and add Polish translations
function processPolishFrontmatter(frontmatterText) {
  let polishFrontmatter = frontmatterText;

  // Add locale field
  polishFrontmatter += '\nlocale: "pl"';

  // Translate title
  polishFrontmatter = polishFrontmatter.replace(
    /title: "([^"]+)"/,
    (match, title) => `title: "${translateText(title)}"`
  );

  // Translate description
  polishFrontmatter = polishFrontmatter.replace(
    /description_short: "([^"]+)"/,
    (match, desc) => `description_short: "${translateText(desc)}"`
  );

  // Translate administrator
  polishFrontmatter = polishFrontmatter.replace(
    /administrator: "([^"]+)"/,
    (match, admin) => `administrator: "${translateText(admin)}"`
  );

  // Translate category
  polishFrontmatter = polishFrontmatter.replace(
    /mop_category: "([^"]+)"/,
    (match, category) => `mop_category: "${translateText(category)}"`
  );

  // Update featured image
  polishFrontmatter = polishFrontmatter.replace(
    /featured_image: "([^"]+)"/,
    (match, imageUrl) => {
      if (imageUrl.includes('text=')) {
        const updatedUrl = imageUrl.replace(
          /text=([^&"]+)/,
          (textMatch, text) => `text=${encodeURIComponent(translateText(decodeURIComponent(text)))}`
        );
        return `featured_image: "${updatedUrl}"`;
      }
      return match;
    }
  );

  return polishFrontmatter;
}

// Main function to process a file
function processFile(filename) {
  const contentDir = path.join(__dirname, '../src/content/rest-areas');
  const inputPath = path.join(contentDir, filename);
  const outputFilename = `pl-${filename}`;
  const outputPath = path.join(contentDir, outputFilename);

  // Check if input file exists
  if (!fs.existsSync(inputPath)) {
    console.error(`❌ Input file not found: ${filename}`);
    return false;
  }

  // Check if output file already exists
  if (fs.existsSync(outputPath)) {
    console.log(`⚠️  Polish translation already exists: ${outputFilename}`);
    return false;
  }

  try {
    // Read and parse the English file
    const content = fs.readFileSync(inputPath, 'utf-8');
    const { frontmatterText, content: markdownContent } = parseMdFile(content);

    // Generate Polish versions
    const polishFrontmatter = processPolishFrontmatter(frontmatterText);
    const polishContent = generatePolishContent(markdownContent);

    // Create the Polish file
    const polishFile = `---\n${polishFrontmatter}\n---\n${polishContent}`;

    fs.writeFileSync(outputPath, polishFile, 'utf-8');
    console.log(`✅ Created Polish translation: ${outputFilename}`);
    return true;
  } catch (error) {
    console.error(`❌ Error processing ${filename}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const contentDir = path.join(__dirname, '../src/content/rest-areas');

  if (args.length > 0) {
    // Process specific file
    const filename = args[0];
    processFile(filename);
  } else {
    // Process all English files
    console.log('🔍 Scanning for English rest area files...');

    const files = fs.readdirSync(contentDir)
      .filter(file => file.endsWith('.md') && !file.startsWith('pl-'))
      .sort();

    if (files.length === 0) {
      console.log('📭 No English files found to translate.');
      return;
    }

    console.log(`📝 Found ${files.length} English files to process:`);
    files.forEach(file => console.log(`   - ${file}`));
    console.log('');

    let processed = 0;
    let created = 0;

    for (const file of files) {
      processed++;
      if (processFile(file)) {
        created++;
      }
    }

    console.log('');
    console.log(`📊 Summary: Processed ${processed} files, created ${created} Polish translations.`);
  }
}

// Run the script
main();
