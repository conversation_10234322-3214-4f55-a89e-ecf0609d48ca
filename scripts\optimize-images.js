#!/usr/bin/env node

/**
 * Image optimization script for generating responsive images in multiple formats
 * Generates WebP and AVIF versions of images in various sizes
 */

import sharp from 'sharp';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SIZES = [640, 768, 1024, 1280, 1536, 1920];
const FORMATS = [
  { ext: 'avif', options: { quality: 50, effort: 6 } },
  { ext: 'webp', options: { quality: 80, effort: 6 } },
  { ext: 'jpg', options: { quality: 85, mozjpeg: true } }
];

const INPUT_DIR = path.join(__dirname, '../public/images');
const OUTPUT_DIR = path.join(__dirname, '../public/images');

/**
 * Optimize a single image file
 */
async function optimizeImage(inputPath, outputDir, filename) {
  const baseName = path.parse(filename).name;
  const inputImage = sharp(inputPath);
  const metadata = await inputImage.metadata();
  
  console.log(`Optimizing ${filename} (${metadata.width}x${metadata.height})`);
  
  // Generate images for each size and format
  for (const size of SIZES) {
    // Skip if the original image is smaller than the target size
    if (metadata.width && metadata.width < size) continue;
    
    for (const format of FORMATS) {
      const outputFilename = `${baseName}-${size}w.${format.ext}`;
      const outputPath = path.join(outputDir, outputFilename);
      
      try {
        await inputImage
          .resize(size, null, {
            withoutEnlargement: true,
            fit: 'inside'
          })
          .toFormat(format.ext, format.options)
          .toFile(outputPath);
        
        const stats = await fs.stat(outputPath);
        console.log(`  Generated ${outputFilename} (${Math.round(stats.size / 1024)}KB)`);
      } catch (error) {
        console.error(`  Error generating ${outputFilename}:`, error.message);
      }
    }
  }
}

/**
 * Main optimization function
 */
async function optimizeImages() {
  try {
    // Ensure output directory exists
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    
    // List of specific images to optimize
    const imagesToOptimize = [
      'home-background.jpg'
    ];
    
    console.log('Starting image optimization...');
    
    for (const filename of imagesToOptimize) {
      const inputPath = path.join(INPUT_DIR, filename);
      
      try {
        await fs.access(inputPath);
        await optimizeImage(inputPath, OUTPUT_DIR, filename);
      } catch (error) {
        console.error(`Error processing ${filename}:`, error.message);
      }
    }
    
    console.log('Image optimization completed!');
    
    // Generate a summary
    const files = await fs.readdir(OUTPUT_DIR);
    const optimizedFiles = files.filter(file => 
      file.includes('-') && (file.endsWith('.webp') || file.endsWith('.avif'))
    );
    
    console.log(`\nGenerated ${optimizedFiles.length} optimized images:`);
    for (const file of optimizedFiles.slice(0, 10)) { // Show first 10
      const stats = await fs.stat(path.join(OUTPUT_DIR, file));
      console.log(`  ${file} (${Math.round(stats.size / 1024)}KB)`);
    }
    
    if (optimizedFiles.length > 10) {
      console.log(`  ... and ${optimizedFiles.length - 10} more`);
    }
    
  } catch (error) {
    console.error('Error during image optimization:', error);
    process.exit(1);
  }
}

// Run the optimization
optimizeImages();
