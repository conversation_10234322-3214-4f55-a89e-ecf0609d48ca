@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-primary-50: #f5f3ff;
  --color-primary-100: #ede9fe;
  --color-primary-200: #ddd6fe;
  --color-primary-300: #c4b5fd;
  --color-primary-400: #a78bfa;
  --color-primary-500: #8b5cf6;
  --color-primary-600: #7c3aed;
  --color-primary-700: #6d28d9;
  --color-primary-800: #5b21b6;
  --color-primary-900: #4c1d95;
  --color-primary-950: #2e1065;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;

  --color-accent-50: #fcfdf5;
  --color-accent-100: #f8fadc;
  --color-accent-200: #f3f7b0;
  --color-accent-300: #eef484;
  --color-accent-400: #e6ee54;
  --color-accent-500: #d9e11a;
  --color-accent-600: #bbc40f;
  --color-accent-700: #99a10d;
  --color-accent-800: #7c8012;
  --color-accent-900: #676a14;
  --color-accent-950: #373b05;

  --color-warning-50: #fefce8;
  --color-warning-100: #fef9c3;
  --color-warning-200: #fef08a;
  --color-warning-300: #fde047;
  --color-warning-400: #facc15;
  --color-warning-500: #eab308;
  --color-warning-600: #ca8a04;
  --color-warning-700: #a16207;
  --color-warning-800: #854d0e;
  --color-warning-900: #713f12;
  --color-warning-950: #422006;

  --font-sans: Inter, system-ui, sans-serif;
  --font-display: Lexend, system-ui, sans-serif;

  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-in-out;
  --animate-slide-down: slideDown 0.5s ease-in-out;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes slideUp {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes slideDown {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility btn {
  @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 focus:outline-hidden;
}

@utility btn-primary {
  @apply btn bg-accent-400 hover:bg-accent-500 text-black shadow-md hover:shadow-lg;
}

@utility btn-secondary {
  @apply btn bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg;
}

@utility btn-accent {
  @apply btn bg-accent-500 hover:bg-accent-600 text-black shadow-md hover:shadow-lg;
}

@utility btn-outline {
  @apply btn border-2 border-secondary-300 dark:border-secondary-700 hover:bg-secondary-100 dark:hover:bg-secondary-800 text-secondary-900 dark:text-secondary-100;
}

@utility container-custom {
  @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
}

@utility section {
  @apply py-16 md:py-24;
}

@utility content-section {
  @apply py-20 md:py-24;
}

@utility card {
  @apply bg-white dark:bg-secondary-900 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-secondary-200 dark:border-secondary-800;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }

h1 {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6;
}

h2 {
  @apply text-3xl md:text-4xl font-bold mb-5;
}

h3 {
  @apply text-2xl md:text-3xl font-semibold mb-4;
}

h4 {
  @apply text-xl md:text-2xl font-semibold mb-4;
}

p {
  @apply text-base md:text-lg leading-relaxed mt-4 mb-6;
}

ul, ol {
  @apply mb-6;
}
}

/* Animations */
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  @apply animate-slide-up;
}

.slide-down {
  @apply animate-slide-down;
}

/* Accessibility */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Focus styles for keyboard navigation */
:focus-visible {
  @apply outline-hidden ring-2 ring-primary-500 dark:ring-primary-400;
}

/* Fix cursor pointer issue - reset all elements to default, then apply pointer to interactive elements */
* {
  cursor: default !important;
}

/* Interactive elements should have pointer cursor */
a, button, [role="button"], input[type="submit"], input[type="button"], input[type="reset"],
select, .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-accent,
[x-data] button, [x-data] select {
  cursor: pointer !important;
}

/* Text input elements should have text cursor */
input[type="text"], input[type="email"], input[type="password"], input[type="search"],
input[type="url"], input[type="tel"], input[type="number"], textarea {
  cursor: text !important;
}
