---
/**
 * Google Ads Tracking Component
 * Implements Google Ads conversion tracking and remarketing
 */

export interface Props {
  adsId?: string;
  conversionId?: string;
  enableRemarketing?: boolean;
  enableConversions?: boolean;
}

const {
  adsId = import.meta.env.PUBLIC_GOOGLE_ADS_ID,
  conversionId = import.meta.env.PUBLIC_GOOGLE_ADS_CONVERSION_ID,
  enableRemarketing = true,
  enableConversions = true
} = Astro.props;

// Only load in production or when explicitly enabled in development
const isProduction = import.meta.env.PROD;
const enableDevAnalytics = import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true';
const enableAnalytics = import.meta.env.PUBLIC_ENABLE_ANALYTICS !== 'false';
const shouldLoadAds = (adsId || conversionId) && enableAnalytics && (isProduction || enableDevAnalytics);
---

{shouldLoadAds && (
  <>
    <!-- Google Ads Global Site Tag (if different from GA4) -->
    {adsId && adsId !== import.meta.env.PUBLIC_GA4_MEASUREMENT_ID && (
      <script async src={`https://www.googletagmanager.com/gtag/js?id=${adsId}`}></script>
    )}
    
    <script is:inline define:vars={{ 
      adsId, 
      conversionId,
      enableRemarketing,
      enableConversions
    }}>
      // Initialize dataLayer if not already done
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      
      // Configure Google Ads if we have an Ads ID
      if (adsId) {
        gtag('config', adsId, {
          // Privacy settings - will be updated based on consent
          allow_ad_personalization_signals: false,
          restricted_data_processing: true
        });
      }

      // Configure conversion tracking if we have a conversion ID
      if (conversionId && enableConversions) {
        gtag('config', conversionId);
      }

      // Remarketing functions
      if (enableRemarketing) {
        // Track page views for remarketing
        window.trackRemarketingPageView = function(pageData = {}) {
          if (typeof gtag !== 'undefined' && adsId) {
            gtag('event', 'page_view', {
              send_to: adsId,
              custom_parameters: {
                page_type: pageData.page_type || 'unknown',
                language: pageData.language || 'en',
                content_category: pageData.content_category || 'general'
              }
            });
          }
        };

        // Track rest area views for remarketing
        window.trackRemarketingRestAreaView = function(restAreaData) {
          if (typeof gtag !== 'undefined' && adsId) {
            gtag('event', 'view_item', {
              send_to: adsId,
              value: 0,
              currency: 'EUR',
              custom_parameters: {
                item_id: restAreaData.id || restAreaData.slug,
                item_name: restAreaData.name,
                item_category: 'rest_area',
                highway: restAreaData.highway_tag || 'unknown',
                region: restAreaData.region || 'unknown',
                amenities: JSON.stringify(restAreaData.amenities || {})
              }
            });
          }
        };

        // Track location searches for remarketing
        window.trackRemarketingSearch = function(searchData) {
          if (typeof gtag !== 'undefined' && adsId) {
            gtag('event', 'search', {
              send_to: adsId,
              search_term: searchData.term || '',
              custom_parameters: {
                search_type: searchData.type || 'location',
                search_country: searchData.country || 'PL',
                search_region: searchData.region || '',
                search_highway: searchData.highway || ''
              }
            });
          }
        };
      }

      // Conversion tracking functions
      if (enableConversions && conversionId) {
        // Track location search as conversion
        window.trackLocationSearchConversion = function(searchData) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
              send_to: conversionId,
              value: 1.0,
              currency: 'EUR',
              custom_parameters: {
                search_type: searchData.type || 'location',
                search_country: searchData.country || 'PL',
                search_region: searchData.region || '',
                search_highway: searchData.highway || '',
                results_count: searchData.results_count || 0
              }
            });
          }
        };

        // Track rest area view as conversion
        window.trackRestAreaViewConversion = function(restAreaData) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
              send_to: conversionId,
              value: 1.0,
              currency: 'EUR',
              custom_parameters: {
                rest_area_id: restAreaData.id || restAreaData.slug,
                rest_area_name: restAreaData.name,
                highway: restAreaData.highway_tag || 'unknown',
                region: restAreaData.region || 'unknown'
              }
            });
          }
        };

        // Track contact/engagement as conversion
        window.trackEngagementConversion = function(engagementData) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
              send_to: conversionId,
              value: 2.0,
              currency: 'EUR',
              custom_parameters: {
                engagement_type: engagementData.type || 'unknown',
                engagement_value: engagementData.value || '',
                page_type: engagementData.page_type || 'unknown'
              }
            });
          }
        };

        // Track newsletter signup or similar high-value actions
        window.trackHighValueConversion = function(actionData) {
          if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
              send_to: conversionId,
              value: 5.0,
              currency: 'EUR',
              custom_parameters: {
                action_type: actionData.type || 'unknown',
                action_value: actionData.value || '',
                user_type: actionData.user_type || 'visitor'
              }
            });
          }
        };
      }

      // Update consent settings for Google Ads
      window.updateAdsConsent = function(consentSettings) {
        if (adsId) {
          gtag('config', adsId, {
            allow_ad_personalization_signals: consentSettings.advertising || false,
            restricted_data_processing: !consentSettings.advertising
          });
        }

        // Update consent for conversion tracking
        gtag('consent', 'update', {
          'ad_storage': consentSettings.advertising ? 'granted' : 'denied',
          'ad_user_data': consentSettings.advertising ? 'granted' : 'denied',
          'ad_personalization': consentSettings.advertising ? 'granted' : 'denied'
        });
      };

      // Set default consent state for ads (denied until user provides consent)
      gtag('consent', 'default', {
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied'
      });
    </script>

    <!-- Enhanced Conversions for better tracking accuracy -->
    {enableConversions && conversionId && (
      <script is:inline>
        // Enhanced conversions setup
        document.addEventListener('DOMContentLoaded', function() {
          // Track form submissions as potential conversions
          document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.tagName === 'FORM') {
              const formType = form.dataset.conversionType || 'form_submit';
              
              if (typeof window.trackEngagementConversion === 'function') {
                window.trackEngagementConversion({
                  type: formType,
                  value: form.id || form.className || 'unknown_form',
                  page_type: document.querySelector('meta[name="page-type"]')?.content || 'unknown'
                });
              }
            }
          });

          // Track high-engagement actions (time on page, scroll depth, etc.)
          let highEngagementTracked = false;
          let startTime = Date.now();

          function trackHighEngagement() {
            if (highEngagementTracked) return;
            
            const timeOnPage = (Date.now() - startTime) / 1000;
            const scrollPercent = Math.round(
              ((window.pageYOffset || document.documentElement.scrollTop) / 
               (document.documentElement.scrollHeight - window.innerHeight)) * 100
            );

            // Consider high engagement if user spends >30 seconds and scrolls >50%
            if (timeOnPage > 30 && scrollPercent > 50) {
              highEngagementTracked = true;
              
              if (typeof window.trackEngagementConversion === 'function') {
                window.trackEngagementConversion({
                  type: 'high_engagement',
                  value: `${Math.round(timeOnPage)}s_${scrollPercent}%`,
                  page_type: document.querySelector('meta[name="page-type"]')?.content || 'unknown'
                });
              }
            }
          }

          // Check for high engagement periodically
          setInterval(trackHighEngagement, 10000); // Check every 10 seconds
          
          // Also check on scroll
          let scrollTimeout;
          window.addEventListener('scroll', function() {
            if (scrollTimeout) clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(trackHighEngagement, 1000);
          });
        });
      </script>
    )}
  </>
)}
