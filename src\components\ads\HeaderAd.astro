---
/**
 * Header Ad Component
 * Optimized ad placement for header area
 */

import AdUnit from '../AdUnit.astro';

export interface Props {
  adSlot?: string;
  showLabel?: boolean;
  className?: string;
}

const {
  adSlot = '1234567890', // Default placeholder - replace with actual ad slot
  showLabel = false,
  className = ''
} = Astro.props;
---

<AdUnit
  adSlot={adSlot}
  format="horizontal"
  placement="header"
  responsive={true}
  lazy={false}
  className={`header-ad ${className}`}
  label={showLabel ? "Advertisement" : undefined}
/>

<style>
  .header-ad {
    /* Header-specific styling */
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
  }
  
  .dark .header-ad {
    border-bottom-color: #374151;
  }
  
  @media (max-width: 768px) {
    .header-ad {
      padding-bottom: 0.5rem;
      margin-bottom: 0.5rem;
    }
  }
</style>
