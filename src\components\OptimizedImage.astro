---
export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  sizes?: string;
  quality?: number;
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  priority = false,
  sizes = '100vw',
  quality = 80
} = Astro.props;

// Generate different sizes for responsive images
const generateSrcSet = (baseSrc: string, format: string) => {
  const baseName = baseSrc.replace(/\.[^/.]+$/, '');
  const sizes = [640, 768, 1024, 1280, 1536, 1920];
  
  return sizes.map(size => {
    return `${baseName}-${size}w.${format} ${size}w`;
  }).join(', ');
};

// Extract base name without extension
const baseName = src.replace(/\.[^/.]+$/, '');
const extension = src.split('.').pop();

// Generate srcsets for different formats
const avifSrcSet = generateSrcSet(src, 'avif');
const webpSrcSet = generateSrcSet(src, 'webp');
const jpegSrcSet = generateSrcSet(src, extension || 'jpg');

// Default fallback image
const fallbackSrc = `${baseName}-1280w.${extension}`;
---

<picture class={className}>
  <!-- AVIF format for modern browsers -->
  <source
    srcset={avifSrcSet}
    sizes={sizes}
    type="image/avif"
  />
  
  <!-- WebP format for most browsers -->
  <source
    srcset={webpSrcSet}
    sizes={sizes}
    type="image/webp"
  />
  
  <!-- JPEG fallback for older browsers -->
  <source
    srcset={jpegSrcSet}
    sizes={sizes}
    type="image/jpeg"
  />
  
  <!-- Fallback img element -->
  <img
    src={fallbackSrc}
    alt={alt}
    width={width}
    height={height}
    loading={priority ? 'eager' : loading}
    decoding="async"
    class={className}
    style="aspect-ratio: auto;"
  />
</picture>

{priority && (
  <!-- Preload the most important image for LCP optimization -->
  <link rel="preload" as="image" href={`${baseName}-1280w.avif`} type="image/avif" />
  <link rel="preload" as="image" href={`${baseName}-1280w.webp`} type="image/webp" />
)}

<style>
  picture {
    display: block;
  }
  
  img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
</style>
