/* Optimized Page Transitions */
.page-transition-wrapper {
  position: relative;
  opacity: 0;
  transform: translate3d(0, 20px, 0);
  transition: opacity 0.5s ease, transform 0.5s ease;
  will-change: opacity, transform;
}

.page-loaded {
  opacity: 1;
  transform: translate3d(0, 0, 0);
}

/* View Transitions Animations */
::view-transition-old(root) {
  animation: 300ms cubic-bezier(0.4, 0, 0.2, 1) both fade-out;
}

::view-transition-new(root) {
  animation: 500ms cubic-bezier(0.4, 0, 0.2, 1) 100ms both fade-in-slide-up;
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fade-in-slide-up {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Element-specific transitions */
main {
  view-transition-name: main-content;
}

/* Only apply to the main site header, not page headers */
header.fixed {
  view-transition-name: site-header;
}

footer {
  view-transition-name: footer;
}

/* Custom transitions for specific elements */
.hero-section {
  view-transition-name: hero;
}

.features-section {
  view-transition-name: features;
}

.testimonials-section {
  view-transition-name: testimonials;
}

/* Optimized Slide transitions with GPU acceleration */
.slide-left-enter {
  transform: translate3d(100%, 0, 0);
  will-change: transform;
}

.slide-left-enter-active {
  transform: translate3d(0, 0, 0);
  transition: transform 0.5s ease-out;
}

.slide-left-exit {
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

.slide-left-exit-active {
  transform: translate3d(-100%, 0, 0);
  transition: transform 0.5s ease-in;
}

.slide-right-enter {
  transform: translate3d(-100%, 0, 0);
  will-change: transform;
}

.slide-right-enter-active {
  transform: translateX(0);
  transition: transform 0.5s ease-out;
}

.slide-right-exit {
  transform: translateX(0);
}

.slide-right-exit-active {
  transform: translateX(100%);
  transition: transform 0.5s ease-in;
} 
