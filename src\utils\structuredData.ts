// Utility functions for generating structured data (JSON-LD)

export interface RestAreaData {
  title: string;
  description_short: string;
  address_line: string;
  coordinates: {
    lat: number;
    lon: number;
  };
  rating?: number;
  amenities: Record<string, boolean>;
  highway_tag: string;
  administrator?: string;
  featured_image: string;
  maps_url?: string;
  work_hours?: string;
  contact_info?: string;
}

export interface LocationData {
  name: string;
  slug: string;
  children?: LocationData[];
}

// Generate structured data for a rest area
export function generateRestAreaStructuredData(
  restArea: RestAreaData,
  url: string,
  locationPath?: string[]
): object {
  const amenityList = Object.entries(restArea.amenities)
    .filter(([_, available]) => available)
    .map(([amenity, _]) => amenity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));

  const workHours = restArea.work_hours ? JSON.parse(restArea.work_hours) : {};
  const contactInfo = restArea.contact_info ? JSON.parse(restArea.contact_info) : {};

  const structuredData: any = {
    "@context": "https://schema.org",
    "@type": "TouristAttraction",
    "name": restArea.title,
    "description": restArea.description_short,
    "url": url,
    "image": restArea.featured_image,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": restArea.address_line,
      "addressCountry": "PL"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": restArea.coordinates.lat,
      "longitude": restArea.coordinates.lon
    },
    "amenityFeature": amenityList.map(amenity => ({
      "@type": "LocationFeatureSpecification",
      "name": amenity
    }))
  };

  // Add rating if available
  if (restArea.rating) {
    structuredData.aggregateRating = {
      "@type": "AggregateRating",
      "ratingValue": restArea.rating,
      "bestRating": 5,
      "worstRating": 1
    };
  }

  // Add opening hours if available
  if (Object.keys(workHours).length > 0) {
    const openingHours = Object.entries(workHours).map(([day, hours]) => {
      const dayMap: Record<string, string> = {
        monday: 'Mo',
        tuesday: 'Tu', 
        wednesday: 'We',
        thursday: 'Th',
        friday: 'Fr',
        saturday: 'Sa',
        sunday: 'Su'
      };
      return `${dayMap[day.toLowerCase()]} ${hours}`;
    });
    structuredData.openingHours = openingHours;
  }

  // Add contact information if available
  if (contactInfo.phone || contactInfo.email) {
    structuredData.contactPoint = {
      "@type": "ContactPoint",
      "telephone": contactInfo.phone,
      "email": contactInfo.email
    };
  }

  return structuredData;
}

// Generate structured data for a location page
export function generateLocationStructuredData(
  location: LocationData,
  url: string,
  restAreaCount: number,
  parentLocation?: LocationData
): object {
  const structuredData: any = {
    "@context": "https://schema.org",
    "@type": "Place",
    "name": location.name,
    "url": url,
    "description": `Highway rest areas in ${location.name}${parentLocation ? `, ${parentLocation.name}` : ''}, Poland. Find ${restAreaCount} rest stops with detailed amenities and information.`,
    "address": {
      "@type": "PostalAddress",
      "addressRegion": location.name,
      "addressCountry": "PL"
    }
  };

  return structuredData;
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{label: string, href: string}>): object {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.label,
      "item": crumb.href.startsWith('http') ? crumb.href : `https://stops24.com${crumb.href}`
    }))
  };
}

// Generate website structured data for homepage
export function generateWebsiteStructuredData(): object {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "stops24.com",
    "description": "Find and explore highway rest areas across Europe. Comprehensive directory with amenities, ratings, and detailed information for travelers.",
    "url": "https://stops24.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://stops24.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "stops24.com",
      "url": "https://stops24.com"
    }
  };
}
