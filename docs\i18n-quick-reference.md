# i18n Quick Reference Guide

## Common Tasks

### Adding a New Translation Key

1. **Add to English file:**
```json
// src/i18n/en.json
{
  "section": {
    "newKey": "English text"
  }
}
```

2. **Add to Polish file:**
```json
// src/i18n/pl.json
{
  "section": {
    "newKey": "Polish text"
  }
}
```

3. **Use in component:**
```astro
---
import { getLangFromUrl, useTranslations } from '../i18n/utils';
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---
<p>{t('section.newKey')}</p>
```

### Creating a New Localized Page

1. **Create English version:**
```astro
// src/pages/new-page.astro
---
import Layout from '../layouts/Layout.astro';
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---
<Layout title={t('page.title')}>
  <h1>{t('page.heading')}</h1>
</Layout>
```

2. **Create Polish version:**
```astro
// src/pages/pl/new-page.astro
---
import Layout from '../../layouts/Layout.astro';
import { getLangFromUrl, useTranslations } from '../../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---
<Layout title={t('page.title')}>
  <h1>{t('page.heading')}</h1>
</Layout>
```

### Language Switching in Components

```astro
---
import LanguageSelector from '../components/LanguageSelector.astro';
---
<header>
  <nav>
    <!-- Navigation items -->
  </nav>
  <LanguageSelector />
</header>
```

## URL Patterns

### Current URL Structure

| Page Type | English | Polish |
|-----------|---------|--------|
| Homepage | `/` | `/pl/` |
| Poland Browse | `/poland/` | `/pl/polska/` |
| Region | `/poland/mazowieckie/` | `/pl/polska/mazowieckie/` |
| City | `/poland/mazowieckie/warszawa/` | `/pl/polska/mazowieckie/warszawa/` |
| Rest Areas | `/rest-areas/` | `/pl/rest-areas/` |

### Language Switching Examples

```typescript
// From English to Polish
getLocalizedPath('/poland/mazowieckie/', 'pl')
// Returns: '/pl/polska/mazowieckie/'

// From Polish to English  
getLocalizedPath('/pl/polska/mazowieckie/', 'en')
// Returns: '/poland/mazowieckie/'
```

## Translation Key Categories

| Category | Purpose | Example |
|----------|---------|---------|
| `nav.*` | Navigation | `nav.home`, `nav.about` |
| `homepage.*` | Homepage content | `homepage.title`, `homepage.subtitle` |
| `search.*` | Search functionality | `search.byLocation`, `search.highway` |
| `common.*` | Shared UI elements | `common.search`, `common.loading` |
| `meta.*` | SEO/metadata | `meta.defaultTitle`, `meta.description` |
| `countries.*` | Country names | `countries.PL` |
| `regions.*` | Region names | `regions.mazowieckie` |
| `urlSlugs.*` | URL mappings | `urlSlugs.poland` |

## Utility Functions

### `useTranslations(lang)`
```astro
---
const t = useTranslations('pl');
---
<h1>{t('homepage.title')}</h1>
```

### `getLangFromUrl(url)`
```astro
---
const currentLang = getLangFromUrl(Astro.url);
// Returns 'en' or 'pl'
---
```

### `getLocalizedPath(path, lang)`
```astro
---
const polishUrl = getLocalizedPath('/poland/', 'pl');
// Returns '/pl/polska/'
---
<a href={polishUrl}>Polish version</a>
```

## Testing Checklist

### Manual Testing
- [ ] Homepage loads in both languages
- [ ] Language selector works correctly
- [ ] All navigation links work in both languages
- [ ] Poland/Polska URLs work correctly
- [ ] Search functionality works in both languages
- [ ] Old `/pl/poland/` URLs return 404

### URL Testing Commands
```bash
# Test English URLs
curl -I http://localhost:4321/
curl -I http://localhost:4321/poland/
curl -I http://localhost:4321/rest-areas/

# Test Polish URLs  
curl -I http://localhost:4321/pl/
curl -I http://localhost:4321/pl/polska/
curl -I http://localhost:4321/pl/rest-areas/

# Verify 404s
curl -I http://localhost:4321/pl/poland/  # Should be 404
```

## Common Issues & Solutions

### Missing Translation Key
**Problem:** Text shows as key instead of translated text
**Solution:** Add the key to all translation files

### Language Switching Not Working
**Problem:** Language selector doesn't change language
**Solution:** Check `getLocalizedPath` function and URL structure

### 404 on Polish Pages
**Problem:** Polish pages return 404
**Solution:** Verify page exists in `src/pages/pl/` directory

### Wrong URL After Language Switch
**Problem:** Language switching goes to wrong page
**Solution:** Check URL mapping in `getLocalizedPath` function

## File Locations Quick Reference

```
src/
├── i18n/
│   ├── en.json              # English translations
│   ├── pl.json              # Polish translations
│   └── utils.ts             # i18n utility functions
├── components/
│   └── LanguageSelector.astro  # Language switching component
├── layouts/
│   └── Layout.astro         # Main layout with language detection
└── pages/
    ├── index.astro          # English homepage
    ├── poland/              # English Poland pages
    └── pl/                  # Polish pages
        ├── index.astro      # Polish homepage
        ├── polska/          # Polish Poland pages
        └── rest-areas/      # Polish rest areas
```

## Adding a New Language (Example: German)

1. **Update Astro config:**
```javascript
// astro.config.mjs
i18n: {
  locales: ['en', 'pl', 'de']
}
```

2. **Create translation file:**
```bash
cp src/i18n/en.json src/i18n/de.json
# Translate all values to German
```

3. **Update types:**
```typescript
// src/i18n/utils.ts
export type Language = 'en' | 'pl' | 'de';
```

4. **Create page structure:**
```bash
mkdir src/pages/de
# Create localized pages
```

5. **Update language selector:**
```astro
<!-- Add German option to LanguageSelector.astro -->
```

This quick reference should help developers quickly implement and maintain the multilingual functionality.
