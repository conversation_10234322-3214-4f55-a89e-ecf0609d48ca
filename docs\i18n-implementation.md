# Multilingual Internationalization (i18n) Implementation

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [File Structure Documentation](#file-structure-documentation)
3. [URL Routing Documentation](#url-routing-documentation)
4. [Component Integration](#component-integration)
5. [Developer Guidelines](#developer-guidelines)
6. [Implementation Summary](#implementation-summary)

## Architecture Overview

### i18n Framework Setup

The stops24.com application uses Astro's built-in internationalization features combined with a custom translation system to support English (default) and Polish languages.

**Astro Configuration (`astro.config.mjs`):**
```javascript
export default defineConfig({
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'pl'],
    routing: {
      prefixDefaultLocale: false
    }
  }
});
```

### Language Detection and Switching

The application implements a multi-layered language detection system:

1. **URL-based detection**: Primary method using URL path analysis
2. **localStorage persistence**: Stores user language preference
3. **Browser locale fallback**: Detects browser language as fallback
4. **Default fallback**: English as ultimate fallback

### URL Structure

The application uses distinct URL patterns to avoid conflicts between country-specific pages and language locales:

**English (Default Language):**
- Homepage: `/`
- Poland locations: `/poland/`
- Rest areas: `/rest-areas/`

**Polish Language:**
- Homepage: `/pl/`
- Poland locations: `/pl/polska/` (localized slug)
- Rest areas: `/pl/rest-areas/`

**Key Design Decision:** 
- English uses `/poland/` for country-specific pages
- Polish uses `/pl/polska/` to provide localized URLs and avoid conflicts with the Polish language prefix

## File Structure Documentation

### Translation Files

**Location:** `src/i18n/`

#### `src/i18n/en.json`
```json
{
  "nav": {
    "home": "Home",
    "browseLocations": "Browse Locations",
    "allRestAreas": "All Rest Areas"
  },
  "homepage": {
    "title": "EU Highway Rest Areas",
    "subtitle": "Find and explore highway rest areas..."
  },
  "search": {
    "byHighway": "By Highway",
    "byLocation": "By Location"
  },
  "countries": {
    "PL": "Poland"
  },
  "regions": {
    "mazowieckie": "Masovian",
    "pomorskie": "Pomeranian"
  },
  "urlSlugs": {
    "poland": "poland",
    "warsaw": "warsaw"
  }
}
```

#### `src/i18n/pl.json`
```json
{
  "nav": {
    "home": "Strona główna",
    "browseLocations": "Przeglądaj lokalizacje",
    "allRestAreas": "Wszystkie miejsca odpoczynku"
  },
  "urlSlugs": {
    "poland": "polska",
    "warsaw": "warszawa"
  }
}
```

### Utility Functions (`src/i18n/utils.ts`)

#### Core Functions:

**`getLangFromUrl(url: URL): Language`**
- Extracts language from URL path
- Returns 'en' or 'pl' based on URL structure

**`useTranslations(lang: Language)`**
- Returns translation function for specified language
- Provides fallback to English if key not found
- Usage: `const t = useTranslations('pl'); t('nav.home')`

**`getLocalizedPath(path: string, lang: Language): string`**
- Converts URLs between languages
- Handles Poland/Polska URL mapping
- Preserves page context during language switching

**`getLocalizedSlug(slug: string, lang: Language): string`**
- Maps URL slugs between languages
- Uses `urlSlugs` translation keys
- Fallback to original slug if no mapping exists

**`detectBrowserLanguage(): Language`**
- Detects user's preferred language
- Checks localStorage first, then browser locale

### Page Structure

#### English Pages Structure:
```
src/pages/
├── index.astro                    # English homepage
├── poland/
│   ├── index.astro               # Poland browse page
│   ├── [...locationSlug].astro   # Location pages
│   └── [...restAreaSlug].astro   # Rest area pages
└── rest-areas/
    └── index.astro               # Rest areas listing
```

#### Polish Pages Structure:
```
src/pages/pl/
├── index.astro                   # Polish homepage
├── polska/                       # Localized "poland" → "polska"
│   ├── index.astro              # Poland browse page (Polish)
│   └── [...locationSlug].astro  # Location pages (Polish)
└── rest-areas/
    └── index.astro              # Rest areas listing (Polish)
```

## URL Routing Documentation

### Localized URL Slug Mapping

The system implements intelligent URL slug mapping to provide localized URLs:

| English URL | Polish URL | Description |
|-------------|------------|-------------|
| `/poland/` | `/pl/polska/` | Country browse page |
| `/poland/mazowieckie/` | `/pl/polska/mazowieckie/` | Region page |
| `/poland/mazowieckie/warszawa/` | `/pl/polska/mazowieckie/warszawa/` | City page |

### Language Switching Logic

The `getLocalizedPath` function handles complex URL transformations:

```typescript
// Example transformations:
getLocalizedPath('/poland/mazowieckie/', 'pl') 
// → '/pl/polska/mazowieckie/'

getLocalizedPath('/pl/polska/mazowieckie/', 'en') 
// → '/poland/mazowieckie/'
```

**Algorithm:**
1. Remove language prefix from current path
2. Detect if path contains Poland/Polska segments
3. Apply appropriate localization based on target language
4. Preserve all location segments after country identifier

### Supported URL Patterns

**English URLs:**
- `/` - Homepage
- `/poland/` - Poland browse page
- `/poland/{region}/` - Region pages
- `/poland/{region}/{city}/` - City pages
- `/rest-areas/` - Rest areas listing

**Polish URLs:**
- `/pl/` - Homepage
- `/pl/polska/` - Poland browse page
- `/pl/polska/{region}/` - Region pages
- `/pl/polska/{region}/{city}/` - City pages
- `/pl/rest-areas/` - Rest areas listing

## Component Integration

### LanguageSelector Component

**Location:** `src/components/LanguageSelector.astro`

**Features:**
- Dropdown interface with language codes (EN, PL)
- Automatic current language detection
- Preserves page context when switching languages
- Uses `getLocalizedPath` for URL transformation

**Integration:**
```astro
---
import LanguageSelector from './LanguageSelector.astro';
---

<div class="header-controls">
  <LanguageSelector />
  <!-- Other controls -->
</div>
```

### Translation Integration in Components

**Header Component Example:**
```astro
---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<nav>
  <a href="/">{t('nav.home')}</a>
  <a href="/poland/">{t('nav.browseLocations')}</a>
</nav>
```

### Translation Key Structure

**Naming Convention:**
- Use dot notation: `section.subsection.key`
- Keep keys descriptive and hierarchical
- Group related translations under common prefixes

**Categories:**
- `nav.*` - Navigation elements
- `homepage.*` - Homepage content
- `search.*` - Search functionality
- `common.*` - Shared UI elements
- `meta.*` - SEO and metadata
- `countries.*` - Country names
- `regions.*` - Region names
- `urlSlugs.*` - URL slug mappings

## Developer Guidelines

### Adding New Languages

1. **Update Astro Configuration:**
```javascript
// astro.config.mjs
i18n: {
  locales: ['en', 'pl', 'de'], // Add new language
}
```

2. **Create Translation File:**
```bash
# Create new translation file
cp src/i18n/en.json src/i18n/de.json
# Translate all values to German
```

3. **Update Type Definitions:**
```typescript
// src/i18n/utils.ts
export type Language = 'en' | 'pl' | 'de';
```

4. **Create Page Structure:**
```bash
mkdir src/pages/de
# Copy and localize pages as needed
```

### Adding New Translation Keys

1. **Add to English file first:**
```json
// src/i18n/en.json
{
  "newSection": {
    "newKey": "English text"
  }
}
```

2. **Add to all other language files:**
```json
// src/i18n/pl.json
{
  "newSection": {
    "newKey": "Polish text"
  }
}
```

3. **Use in components:**
```astro
---
const t = useTranslations(currentLang);
---
<p>{t('newSection.newKey')}</p>
```

### Creating Localized Pages

1. **For new country/region pages:**
```bash
# Create English version first
src/pages/country-name/index.astro

# Create localized versions
src/pages/pl/localized-country-name/index.astro
```

2. **Update URL mapping:**
```json
// Add to urlSlugs in translation files
"urlSlugs": {
  "country-name": "localized-name"
}
```

3. **Update getLocalizedPath function** if new URL patterns are needed.

### Testing Procedures

1. **Manual Testing Checklist:**
   - [ ] All pages load in both languages
   - [ ] Language switching preserves page context
   - [ ] URLs use correct localized slugs
   - [ ] Navigation works in both languages
   - [ ] Search functionality works in both languages

2. **URL Testing:**
   ```bash
   # Test English URLs
   curl -I http://localhost:4321/poland/
   curl -I http://localhost:4321/poland/mazowieckie/
   
   # Test Polish URLs
   curl -I http://localhost:4321/pl/polska/
   curl -I http://localhost:4321/pl/polska/mazowieckie/
   
   # Verify 404s for old URLs
   curl -I http://localhost:4321/pl/poland/  # Should be 404
   ```

3. **Translation Testing:**
   - Verify all UI text is translated
   - Check for missing translation keys (fallback to English)
   - Test special characters and Polish diacritics

## Implementation Summary

### Files Created/Modified

**New Files:**
- `src/i18n/en.json` - English translations
- `src/i18n/pl.json` - Polish translations
- `src/i18n/utils.ts` - i18n utility functions
- `src/components/LanguageSelector.astro` - Language switching component
- `src/pages/pl/index.astro` - Polish homepage
- `src/pages/pl/polska/index.astro` - Polish Poland browse page
- `src/pages/pl/polska/[...locationSlug].astro` - Polish location pages
- `src/pages/pl/rest-areas/index.astro` - Polish rest areas page

**Modified Files:**
- `astro.config.mjs` - Added i18n configuration
- `src/layouts/Layout.astro` - Added language detection
- `src/components/Header.astro` - Added translations and language selector
- `src/pages/index.astro` - Added translations
- `src/pages/poland/index.astro` - Updated for new URL structure
- `src/pages/poland/[...locationSlug].astro` - Updated for new URL structure

### Supported URL Examples

**Working URLs:**

*English:*
- `http://localhost:4321/` - Homepage
- `http://localhost:4321/poland/` - Poland browse
- `http://localhost:4321/poland/mazowieckie/` - Mazowieckie region
- `http://localhost:4321/poland/mazowieckie/warszawa/` - Warsaw city
- `http://localhost:4321/rest-areas/` - Rest areas listing

*Polish:*
- `http://localhost:4321/pl/` - Homepage
- `http://localhost:4321/pl/polska/` - Poland browse
- `http://localhost:4321/pl/polska/mazowieckie/` - Mazowieckie region
- `http://localhost:4321/pl/polska/mazowieckie/warszawa/` - Warsaw city
- `http://localhost:4321/pl/rest-areas/` - Rest areas listing

**Deprecated URLs (404):**
- `http://localhost:4321/pl/poland/` - Old Polish Poland URL

### Key Features Implemented

1. ✅ **Automatic language detection** from URL, localStorage, and browser
2. ✅ **Seamless language switching** with context preservation
3. ✅ **Localized URL slugs** (poland ↔ polska)
4. ✅ **Comprehensive translation system** with fallbacks
5. ✅ **SEO-friendly URLs** for both languages
6. ✅ **Conflict-free routing** between country pages and language locales
7. ✅ **Extensible architecture** for adding more languages

The implementation provides a robust foundation for multilingual support that can be easily extended to support additional languages while maintaining excellent user experience and SEO optimization.
