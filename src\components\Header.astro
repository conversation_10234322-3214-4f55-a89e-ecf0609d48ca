---
import LanguageSelector from './LanguageSelector.astro';
import { getLangFromUrl, useTranslations, getLocalizedPath, type Language } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Helper to generate localized hrefs
const localizeHref = (path: string) => getLocalizedPath(path, currentLang as Language);
---
<header class="fixed w-full bg-white/90 dark:bg-secondary-950/90 backdrop-blur-xs z-50 py-4 transition-colors duration-300">
  <div class="container-custom flex items-center justify-between">
    <a href={localizeHref('/')} class="flex items-center" aria-label="Go to homepage">
      <!-- Highway Rest Area Logo SVG -->
      <svg class="h-10 w-auto" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
        <!-- Background circle -->
        <circle cx="50" cy="50" r="48" fill="#f0f9ff" class="dark:fill-slate-800" />

        <!-- Road -->
        <rect x="20" y="45" width="60" height="10" fill="#64748b" class="dark:fill-slate-600" />
        <rect x="20" y="48" width="60" height="2" fill="#fbbf24" class="dark:fill-yellow-400" />
        <rect x="20" y="50" width="60" height="2" fill="#fbbf24" class="dark:fill-yellow-400" />

        <!-- Rest area building -->
        <rect x="35" y="25" width="30" height="20" fill="#94a3b8" class="dark:fill-slate-500" />
        <polygon points="35,25 50,15 65,25" fill="#64748b" class="dark:fill-slate-600" />

        <!-- Windows -->
        <rect x="40" y="30" width="4" height="6" fill="#1e293b" class="dark:fill-white" />
        <rect x="46" y="30" width="4" height="6" fill="#1e293b" class="dark:fill-white" />
        <rect x="52" y="30" width="4" height="6" fill="#1e293b" class="dark:fill-white" />
        <rect x="58" y="30" width="4" height="6" fill="#1e293b" class="dark:fill-white" />

        <!-- Door -->
        <rect x="47" y="38" width="6" height="7" fill="#475569" class="dark:fill-slate-700" />

        <!-- Parking spaces -->
        <rect x="25" y="60" width="8" height="12" fill="none" stroke="#64748b" stroke-width="1" class="dark:stroke-slate-600" />
        <rect x="35" y="60" width="8" height="12" fill="none" stroke="#64748b" stroke-width="1" class="dark:stroke-slate-600" />
        <rect x="57" y="60" width="8" height="12" fill="none" stroke="#64748b" stroke-width="1" class="dark:stroke-slate-600" />
        <rect x="67" y="60" width="8" height="12" fill="none" stroke="#64748b" stroke-width="1" class="dark:stroke-slate-600" />

        <!-- Decorative circle -->
        <circle cx="50" cy="50" r="46" stroke="#0ea5e9" stroke-width="1.5" fill="none" class="dark:stroke-teal-500" stroke-dasharray="3,2" />
      </svg>
      <span class="ml-2 text-xl font-display font-semibold text-secondary-900 dark:text-white">stops24</span>
    </a>

    <nav class="hidden md:flex items-center space-x-8">
      <a href={localizeHref('/')} class="text-secondary-600 hover:text-primary-600 dark:text-secondary-300 dark:hover:text-primary-400 font-medium transition-colors">{t('nav.home')}</a>
      <a href={localizeHref('/poland/')} class="text-secondary-600 hover:text-primary-600 dark:text-secondary-300 dark:hover:text-primary-400 font-medium transition-colors">{t('nav.browseLocations')}</a>
      <a href={localizeHref('/rest-areas/')} class="text-secondary-600 hover:text-primary-600 dark:text-secondary-300 dark:hover:text-primary-400 font-medium transition-colors">{t('nav.allRestAreas')}</a>
      <a href={localizeHref('/about')} class="text-secondary-600 hover:text-primary-600 dark:text-secondary-300 dark:hover:text-primary-400 font-medium transition-colors">{t('nav.about')}</a>
      <a href={localizeHref('/contact')} class="text-secondary-600 hover:text-primary-600 dark:text-secondary-300 dark:hover:text-primary-400 font-medium transition-colors">{t('nav.contact')}</a>
    </nav>

    <div class="flex items-center space-x-4">
      <!-- Language Selector -->
      <LanguageSelector />

      <!-- Dark Mode Toggle -->
      <button
        x-on:click="darkMode = !darkMode"
        class="p-2 rounded-full text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200 focus:outline-hidden focus:ring-2 focus:ring-primary-500"
        aria-label="Toggle dark mode"
      >
        <svg x-show="!darkMode" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
        <svg x-show="darkMode" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      </button>



      <!-- Mobile Menu Button and Dropdown -->
      <div x-data="{ open: false }">
        <button
          @click="open = !open"
          class="md:hidden p-2 rounded-md text-secondary-600 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-white focus:outline-hidden focus:ring-2 focus:ring-primary-500"
          aria-label="Toggle menu"
        >
          <svg x-show="!open" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <svg x-show="open" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div
          x-show="open"
          x-transition:enter="transition ease-out duration-200"
          x-transition:enter-start="opacity-0 scale-95"
          x-transition:enter-end="opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-150"
          x-transition:leave-start="opacity-100 scale-100"
          x-transition:leave-end="opacity-0 scale-95"
          class="absolute top-16 right-4 w-48 py-2 bg-white dark:bg-secondary-900 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden"
          x-cloak
        >
          <a href={localizeHref('/')} class="block px-4 py-2 text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800">{t('nav.home')}</a>
          <a href={localizeHref('/poland/')} class="block px-4 py-2 text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800">{t('nav.browseLocations')}</a>
          <a href={localizeHref('/rest-areas/')} class="block px-4 py-2 text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800">{t('nav.allRestAreas')}</a>
          <a href={localizeHref('/about')} class="block px-4 py-2 text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800">{t('nav.about')}</a>
          <a href={localizeHref('/contact')} class="block px-4 py-2 text-sm text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800">{t('nav.contact')}</a>


        </div>
      </div>
    </div>
  </div>
</header>

<style>
  /* Add styles for dark mode SVG in the header */
  @media (prefers-color-scheme: dark) {
    .dark\:fill-slate-800 { fill: #1e293b; }
    .dark\:fill-slate-600 { fill: #475569; }
    .dark\:fill-slate-500 { fill: #64748b; }
    .dark\:fill-slate-700 { fill: #334155; }
    .dark\:stroke-slate-300 { stroke: #cbd5e1; }
    .dark\:stroke-teal-500 { stroke: #14b8a6; }
    .dark\:fill-white { fill: #ffffff; }
  }

  /* Add hover animation to the logo */
  header a:hover svg {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }

  /* Hide Alpine.js elements before Alpine loads */
  [x-cloak] { display: none !important; }
</style>
