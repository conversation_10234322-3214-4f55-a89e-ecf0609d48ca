---
export interface Props {
  title: string;
  description: string;
  canonical?: string;
  image?: string;
  imageAlt?: string;
  type?: 'website' | 'article' | 'place';
  noindex?: boolean;
  nofollow?: boolean;
  structuredData?: object;
}

const {
  title,
  description,
  canonical,
  image = '/favicon.svg',
  imageAlt = 'stops24.com - EU Highway Rest Areas Directory',
  type = 'website',
  noindex = false,
  nofollow = false,
  structuredData
} = Astro.props;

// Ensure description is within optimal length
const optimizedDescription = description.length > 160 
  ? description.substring(0, 157) + '...' 
  : description;

// Build canonical URL
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site).href;

// Build full image URL
const fullImageURL = image.startsWith('http') 
  ? image 
  : new URL(image, Astro.site).href;

// Build robots directive
const robotsContent = [
  noindex ? 'noindex' : 'index',
  nofollow ? 'nofollow' : 'follow'
].join(', ');
---

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={optimizedDescription} />
<meta name="robots" content={robotsContent} />
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:title" content={title} />
<meta property="og:description" content={optimizedDescription} />
<meta property="og:image" content={fullImageURL} />
<meta property="og:image:alt" content={imageAlt} />
<meta property="og:site_name" content="stops24.com" />
<meta property="og:locale" content="en_US" />

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={canonicalURL} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={optimizedDescription} />
<meta property="twitter:image" content={fullImageURL} />
<meta property="twitter:image:alt" content={imageAlt} />

<!-- Additional SEO Meta Tags -->
<meta name="author" content="stops24.com" />
<meta name="publisher" content="stops24.com" />
<meta name="theme-color" content="#0ea5e9" />

<!-- Structured Data -->
{structuredData && (
  <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
)}
