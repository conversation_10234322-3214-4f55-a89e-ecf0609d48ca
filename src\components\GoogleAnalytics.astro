---
/**
 * Google Analytics 4 Component
 * Implements GA4 tracking with GDPR compliance and enhanced ecommerce
 */

import { getLangFromUrl } from '../i18n/utils';

export interface Props {
  measurementId?: string;
  enableEnhancedEcommerce?: boolean;
  enableWebVitals?: boolean;
  customDimensions?: Record<string, string>;
}

const {
  measurementId = import.meta.env.PUBLIC_GA4_MEASUREMENT_ID,
  enableEnhancedEcommerce = true,
  enableWebVitals = true,
  customDimensions = {}
} = Astro.props;

// Get current language for tracking
const currentLang = getLangFromUrl(Astro.url);

// Only load in production or when explicitly enabled in development
const isProduction = import.meta.env.PROD;
const enableDevAnalytics = import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true';
const enableAnalytics = import.meta.env.PUBLIC_ENABLE_ANALYTICS !== 'false';
const shouldLoadAnalytics = measurementId && enableAnalytics && (isProduction || enableDevAnalytics);

// Get page type from URL for custom dimensions
const getPageType = (url: URL) => {
  const pathname = url.pathname;
  if (pathname === '/' || pathname === '/pl/') return 'homepage';
  if (pathname.includes('/poland/') || pathname.includes('/polska/')) return 'location_browse';
  if (pathname.includes('/rest-areas/')) return 'rest_areas_listing';
  if (pathname.includes('/privacy-policy')) return 'privacy_policy';
  if (pathname.includes('/terms-of-service')) return 'terms_of_service';
  return 'other';
};

const pageType = getPageType(Astro.url);
---

{shouldLoadAnalytics && (
  <>
    <!-- Google Analytics 4 Global Site Tag -->
    <script async src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}></script>
    
    <script is:inline define:vars={{ 
      measurementId, 
      currentLang, 
      pageType, 
      enableEnhancedEcommerce,
      enableWebVitals,
      customDimensions 
    }}>
      // Initialize dataLayer
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      
      // Configure GA4
      gtag('js', new Date());
      gtag('config', measurementId, {
        // Basic configuration
        page_title: document.title,
        page_location: window.location.href,
        language: currentLang,
        
        // Custom dimensions
        custom_map: {
          'dimension1': 'page_type',
          'dimension2': 'language',
          'dimension3': 'location_hierarchy'
        },
        
        // Enhanced ecommerce
        ...(enableEnhancedEcommerce && {
          send_page_view: false, // We'll send manually with enhanced data
        }),
        
        // Privacy settings
        anonymize_ip: true,
        allow_google_signals: false, // Will be enabled based on consent
        allow_ad_personalization_signals: false, // Will be enabled based on consent
        
        // Additional custom dimensions
        ...customDimensions
      });

      // Send enhanced page view with custom data
      gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        page_type: pageType,
        language: currentLang,
        content_group1: pageType,
        content_group2: currentLang
      });

      // Enhanced ecommerce tracking for rest area views
      if (enableEnhancedEcommerce) {
        // Track rest area views as item views
        window.trackRestAreaView = function(restAreaData) {
          gtag('event', 'view_item', {
            currency: 'EUR',
            value: 0,
            items: [{
              item_id: restAreaData.id || restAreaData.slug,
              item_name: restAreaData.name,
              item_category: 'Rest Area',
              item_category2: restAreaData.highway_tag || 'Unknown Highway',
              item_category3: restAreaData.region || 'Unknown Region',
              item_variant: restAreaData.direction || 'Unknown Direction',
              location_id: restAreaData.location_id || 'unknown'
            }]
          });
        };

        // Track location searches as search events
        window.trackLocationSearch = function(searchData) {
          gtag('event', 'search', {
            search_term: searchData.term || '',
            search_type: searchData.type || 'location', // 'location' or 'highway'
            search_country: searchData.country || 'PL',
            search_region: searchData.region || '',
            search_highway: searchData.highway || ''
          });
        };
      }

      // Track language switching
      window.trackLanguageSwitch = function(fromLang, toLang) {
        gtag('event', 'language_switch', {
          event_category: 'User Interaction',
          event_label: `${fromLang}_to_${toLang}`,
          from_language: fromLang,
          to_language: toLang
        });
      };

      // Track navigation interactions
      window.trackNavigation = function(linkText, linkUrl, linkType = 'internal') {
        gtag('event', 'click', {
          event_category: 'Navigation',
          event_label: linkText,
          link_url: linkUrl,
          link_type: linkType
        });
      };

      // Track dark mode toggle
      window.trackDarkModeToggle = function(mode) {
        gtag('event', 'dark_mode_toggle', {
          event_category: 'User Preference',
          event_label: mode,
          dark_mode: mode === 'dark'
        });
      };

      // Update consent settings
      window.updateGAConsent = function(consentSettings) {
        gtag('consent', 'update', {
          'analytics_storage': consentSettings.analytics ? 'granted' : 'denied',
          'ad_storage': consentSettings.advertising ? 'granted' : 'denied',
          'ad_user_data': consentSettings.advertising ? 'granted' : 'denied',
          'ad_personalization': consentSettings.advertising ? 'granted' : 'denied'
        });

        // Update Google Signals and ad personalization based on consent
        if (consentSettings.analytics) {
          gtag('config', measurementId, {
            allow_google_signals: consentSettings.advertising,
            allow_ad_personalization_signals: consentSettings.advertising
          });
        }
      };

      // Set default consent state (denied until user provides consent)
      gtag('consent', 'default', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied',
        'wait_for_update': 500
      });
    </script>

    {enableWebVitals && (
      <!-- Optimized Core Web Vitals tracking with local script -->
      <script src="/js/web-vitals-ga.js" defer></script>
    )}

    <!-- Enhanced event tracking for user interactions -->
    <script is:inline>
      document.addEventListener('DOMContentLoaded', function() {
        // Track internal link clicks
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a');
          if (link && link.hostname === window.location.hostname) {
            const linkText = link.textContent?.trim() || link.getAttribute('aria-label') || 'Unknown';
            const linkUrl = link.href;
            
            if (typeof window.trackNavigation === 'function') {
              window.trackNavigation(linkText, linkUrl, 'internal');
            }
          }
        });

        // Track external link clicks
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a');
          if (link && link.hostname !== window.location.hostname && link.href.startsWith('http')) {
            const linkText = link.textContent?.trim() || link.getAttribute('aria-label') || 'Unknown';
            const linkUrl = link.href;
            
            if (typeof gtag !== 'undefined') {
              gtag('event', 'click', {
                event_category: 'External Link',
                event_label: linkText,
                link_url: linkUrl,
                link_domain: link.hostname
              });
            }
          }
        });

        // Track form submissions
        document.addEventListener('submit', function(e) {
          const form = e.target;
          if (form.tagName === 'FORM') {
            const formId = form.id || form.className || 'unknown_form';
            
            if (typeof gtag !== 'undefined') {
              gtag('event', 'form_submit', {
                event_category: 'Form Interaction',
                event_label: formId,
                form_id: formId
              });
            }
          }
        });

        // Track scroll depth
        let maxScrollDepth = 0;
        let scrollDepthMarkers = [25, 50, 75, 90, 100];
        let trackedMarkers = new Set();

        function trackScrollDepth() {
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const docHeight = document.documentElement.scrollHeight - window.innerHeight;
          const scrollPercent = Math.round((scrollTop / docHeight) * 100);
          
          if (scrollPercent > maxScrollDepth) {
            maxScrollDepth = scrollPercent;
            
            // Track milestone markers
            scrollDepthMarkers.forEach(marker => {
              if (scrollPercent >= marker && !trackedMarkers.has(marker)) {
                trackedMarkers.add(marker);
                
                if (typeof gtag !== 'undefined') {
                  gtag('event', 'scroll', {
                    event_category: 'User Engagement',
                    event_label: `${marker}%`,
                    value: marker,
                    scroll_depth: marker
                  });
                }
              }
            });
          }
        }

        // Throttled scroll tracking
        let scrollTimeout;
        window.addEventListener('scroll', function() {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(trackScrollDepth, 250);
        });
      });
    </script>
  </>
)}
