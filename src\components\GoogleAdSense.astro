---
/**
 * Google AdSense Component
 * Implements AdSense ad serving with GDPR compliance and performance optimization
 */

export interface Props {
  publisherId?: string;
  enableAutoAds?: boolean;
  enableManualAds?: boolean;
  testMode?: boolean;
}

const {
  publisherId = import.meta.env.PUBLIC_ADSENSE_PUBLISHER_ID,
  enableAutoAds = import.meta.env.PUBLIC_ADSENSE_AUTO_ADS === 'true',
  enableManualAds = import.meta.env.PUBLIC_ADSENSE_MANUAL_ADS !== 'false',
  testMode = !import.meta.env.PROD
} = Astro.props;

// Only load in production or when explicitly enabled in development
const isProduction = import.meta.env.PROD;
const enableDevAnalytics = import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true';
const enableAnalytics = import.meta.env.PUBLIC_ENABLE_ANALYTICS !== 'false';
const shouldLoadAdSense = publisherId && enableAnalytics && (isProduction || enableDevAnalytics);
---

{shouldLoadAdSense && (
  <>
    <!-- Google AdSense Script -->
    <script 
      async 
      src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${publisherId}`}
      crossorigin="anonymous"
      data-ad-client={publisherId}
      {testMode && { 'data-adbreak-test': 'on' }}
    ></script>

    <!-- AdSense Configuration and Consent Management -->
    <script is:inline define:vars={{ 
      publisherId, 
      enableAutoAds, 
      enableManualAds,
      testMode 
    }}>
      // Initialize AdSense configuration
      window.adsbygoogle = window.adsbygoogle || [];
      
      // AdSense consent and configuration management
      window.adSenseManager = {
        initialized: false,
        consentGiven: false,
        autoAdsEnabled: enableAutoAds,
        manualAdsEnabled: enableManualAds,
        
        // Initialize AdSense with consent
        init: function(consentSettings) {
          if (this.initialized) return;
          
          this.consentGiven = consentSettings.advertising || false;
          
          if (this.consentGiven) {
            this.enableAdSense();
          } else {
            this.disableAdSense();
          }
          
          this.initialized = true;
        },
        
        // Enable AdSense ads
        enableAdSense: function() {
          try {
            // Configure auto ads if enabled
            if (this.autoAdsEnabled) {
              (adsbygoogle = window.adsbygoogle || []).push({
                google_ad_client: publisherId,
                enable_page_level_ads: true,
                overlays: {bottom: true},
                // Performance optimization
                page_level_ad_types: ["vignette", "interstitial"]
              });
            }
            
            // Enable manual ads
            if (this.manualAdsEnabled) {
              this.refreshManualAds();
            }
            
            console.log('AdSense: Ads enabled with consent');
          } catch (error) {
            console.warn('AdSense: Error enabling ads:', error);
          }
        },
        
        // Disable AdSense ads
        disableAdSense: function() {
          try {
            // Hide all ad units
            const adUnits = document.querySelectorAll('.adsbygoogle');
            adUnits.forEach(function(ad) {
              ad.style.display = 'none';
            });
            
            console.log('AdSense: Ads disabled due to consent');
          } catch (error) {
            console.warn('AdSense: Error disabling ads:', error);
          }
        },
        
        // Refresh manual ad units
        refreshManualAds: function() {
          if (!this.consentGiven || !this.manualAdsEnabled) return;
          
          try {
            const adUnits = document.querySelectorAll('.adsbygoogle:not([data-ad-status])');
            adUnits.forEach(function(ad) {
              if (ad.style.display !== 'none') {
                (adsbygoogle = window.adsbygoogle || []).push({});
              }
            });
          } catch (error) {
            console.warn('AdSense: Error refreshing manual ads:', error);
          }
        },
        
        // Update consent settings
        updateConsent: function(consentSettings) {
          const newConsentState = consentSettings.advertising || false;
          
          if (newConsentState !== this.consentGiven) {
            this.consentGiven = newConsentState;
            
            if (this.consentGiven) {
              this.enableAdSense();
            } else {
              this.disableAdSense();
            }
          }
        },
        
        // Load ad unit dynamically
        loadAdUnit: function(adUnitConfig) {
          if (!this.consentGiven) {
            console.log('AdSense: Cannot load ad unit without consent');
            return;
          }
          
          try {
            const adContainer = document.getElementById(adUnitConfig.containerId);
            if (!adContainer) {
              console.warn('AdSense: Ad container not found:', adUnitConfig.containerId);
              return;
            }
            
            // Create ad unit
            const adUnit = document.createElement('ins');
            adUnit.className = 'adsbygoogle';
            adUnit.style.display = 'block';
            adUnit.setAttribute('data-ad-client', publisherId);
            adUnit.setAttribute('data-ad-slot', adUnitConfig.adSlot);
            adUnit.setAttribute('data-ad-format', adUnitConfig.format || 'auto');
            adUnit.setAttribute('data-full-width-responsive', 'true');
            
            // Add responsive attributes
            if (adUnitConfig.responsive) {
              adUnit.setAttribute('data-ad-format', 'fluid');
              adUnit.setAttribute('data-ad-layout-key', adUnitConfig.layoutKey || '-6t+ed+2i-1n-4w');
            }
            
            adContainer.appendChild(adUnit);
            
            // Initialize the ad
            (adsbygoogle = window.adsbygoogle || []).push({});
            
            console.log('AdSense: Ad unit loaded:', adUnitConfig.containerId);
          } catch (error) {
            console.warn('AdSense: Error loading ad unit:', error);
          }
        }
      };
      
      // Set default consent state (disabled until consent given)
      window.adSenseManager.init({ advertising: false });
      
      // Listen for consent updates
      window.addEventListener('cookieConsentUpdated', function(event) {
        window.adSenseManager.updateConsent(event.detail);
      });
      
      // Performance optimization: lazy load ads on scroll
      if ('IntersectionObserver' in window) {
        const adObserver = new IntersectionObserver(function(entries) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting && window.adSenseManager.consentGiven) {
              const adUnit = entry.target;
              if (!adUnit.hasAttribute('data-ad-status')) {
                try {
                  (adsbygoogle = window.adsbygoogle || []).push({});
                  adUnit.setAttribute('data-ad-status', 'loaded');
                } catch (error) {
                  console.warn('AdSense: Error loading lazy ad:', error);
                }
              }
              adObserver.unobserve(adUnit);
            }
          });
        }, {
          rootMargin: '100px' // Load ads 100px before they come into view
        });
        
        // Observe ad units for lazy loading
        document.addEventListener('DOMContentLoaded', function() {
          const lazyAds = document.querySelectorAll('.adsbygoogle[data-lazy="true"]');
          lazyAds.forEach(function(ad) {
            adObserver.observe(ad);
          });
        });
      }
    </script>

    <!-- AdSense Error Handling -->
    <script is:inline>
      // Handle AdSense loading errors
      window.addEventListener('error', function(event) {
        if (event.target && event.target.src && event.target.src.includes('googlesyndication.com')) {
          console.warn('AdSense: Script loading error, ads may be blocked');
          
          // Notify that ads are blocked (for analytics)
          if (typeof gtag !== 'undefined') {
            gtag('event', 'adsense_blocked', {
              event_category: 'AdSense',
              event_label: 'Script blocked',
              non_interaction: true
            });
          }
        }
      });
      
      // Monitor ad performance
      if (typeof window.adSenseManager !== 'undefined') {
        // Track ad load success/failure
        const originalPush = window.adsbygoogle.push;
        window.adsbygoogle.push = function(params) {
          try {
            const result = originalPush.call(this, params);
            
            // Track successful ad load
            if (typeof gtag !== 'undefined') {
              gtag('event', 'adsense_ad_loaded', {
                event_category: 'AdSense',
                event_label: 'Ad loaded successfully',
                non_interaction: true
              });
            }
            
            return result;
          } catch (error) {
            console.warn('AdSense: Ad loading error:', error);
            
            // Track ad load failure
            if (typeof gtag !== 'undefined') {
              gtag('event', 'adsense_ad_error', {
                event_category: 'AdSense',
                event_label: error.message || 'Unknown error',
                non_interaction: true
              });
            }
            
            throw error;
          }
        };
      }
    </script>

    <!-- Core Web Vitals optimization for ads -->
    <script is:inline>
      // Optimize ad loading for Core Web Vitals
      document.addEventListener('DOMContentLoaded', function() {
        // Delay ad loading slightly to improve LCP
        setTimeout(function() {
          if (window.adSenseManager && window.adSenseManager.consentGiven) {
            window.adSenseManager.refreshManualAds();
          }
        }, 100);
        
        // Monitor CLS impact from ads
        if ('PerformanceObserver' in window) {
          try {
            const clsObserver = new PerformanceObserver(function(list) {
              for (const entry of list.getEntries()) {
                if (entry.hadRecentInput) continue;
                
                // Check if layout shift was caused by ads
                const adElements = document.querySelectorAll('.adsbygoogle');
                let adCausedShift = false;
                
                for (const source of entry.sources) {
                  for (const ad of adElements) {
                    if (ad.contains(source.node)) {
                      adCausedShift = true;
                      break;
                    }
                  }
                  if (adCausedShift) break;
                }
                
                if (adCausedShift && typeof gtag !== 'undefined') {
                  gtag('event', 'adsense_cls_impact', {
                    event_category: 'AdSense',
                    event_label: 'Layout shift caused by ad',
                    value: Math.round(entry.value * 1000),
                    non_interaction: true
                  });
                }
              }
            });
            
            clsObserver.observe({ type: 'layout-shift', buffered: true });
          } catch (error) {
            console.warn('AdSense: CLS monitoring error:', error);
          }
        }
      });
    </script>
  </>
)}
