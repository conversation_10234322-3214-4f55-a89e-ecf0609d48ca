import type { APIRoute } from 'astro';
import { getCollection } from 'astro:content';

export const GET: APIRoute = async ({ url, request }) => {
  try {
    // Derive language
    const searchParams = url.searchParams;
    let lang = searchParams.get('lang');

    // Allow explicit header from client as the authoritative signal (overrides query)
    const headerLang = request.headers.get('x-current-lang');
    if (headerLang && (headerLang === 'pl' || headerLang === 'en')) {
      lang = headerLang;
    }

    // If still no explicit lang, infer from Referer and Accept-Language headers
    if (!lang) {
      const referer = request.headers.get('referer') || '';
      let inferred: string | null = null;
      try {
        if (referer) {
          const refUrl = new URL(referer);
          const p = refUrl.pathname || '';
          if (p.startsWith('/pl/')) inferred = 'pl';
          else if (p === '/pl') inferred = 'pl';
        }
      } catch {}

      if (!inferred) {
        const acceptLang = request.headers.get('accept-language') || '';
        if (/\bpl\b/i.test(acceptLang)) inferred = 'pl';
      }

      lang = inferred || 'en';
    }

    // Debug logging
    console.log(`API: Full URL: ${url}`);
    console.log(`API: Query params: ${searchParams.toString()}`);
    console.log(`API: Using lang=${lang}, searching for entries starting with "${lang}/"`);

    // Fetch rest areas for the specified language
    const restAreas = await getCollection('rest-areas', ({ id }) =>
      id.startsWith(`${lang}/`)
    );

    // Debug logging
    console.log(`API: Found ${restAreas.length} rest areas for language "${lang}"`);
    if (restAreas.length > 0) {
      console.log(`API: Sample IDs: ${restAreas.slice(0, 3).map(ra => ra.id).join(', ')}`);
      console.log(`API: Sample descriptions: ${restAreas.slice(0, 2).map(ra => ra.data.description_short).join(' | ')}`);
    }
    
    // Transform data to include only necessary fields for geolocation
    const transformedData = restAreas.map(restArea => ({
      id: restArea.id,
      data: {
        title: restArea.data.title,
        description_short: restArea.data.description_short,
        address_line: restArea.data.address_line,
        coordinates: restArea.data.coordinates,
        highway_tag: restArea.data.highway_tag,
        amenities: restArea.data.amenities
      }
    }));
    
    return new Response(JSON.stringify(transformedData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate' // Disable caching for debugging
      }
    });
  } catch (error) {
    console.error('Error fetching rest areas:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch rest areas' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
