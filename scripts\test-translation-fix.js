#!/usr/bin/env node

/**
 * Test script to validate the improved translation logic
 * This script tests the new translation system before applying it to all files
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the translation logic from the main script
// We'll copy the functions here for testing

// Complete sentence translations for proper Polish content
const completeSentenceTranslations = {
  'Located on the A4 highway at kilometer marker 399+000 in the Małopolskie region, Aleksandrowice offers essential services for travelers heading towards Katowice. This facility provides a convenient stopping point with various amenities to ensure a comfortable journey.': 'Położony na autostradzie A4 przy słupku kilometrowym 399+000 w województwie małopolskim, MOP Aleksandrowice oferuje niezbędne usługi dla podróżnych zmierzających w kierunku Katowic. Obiekt stanowi wygodny punkt postojowy z różnymi udogodnieniami zapewniającymi komfortową podróż.',
  
  'The facility operates 24/7, making it an ideal stop for travelers at any time. The rest area features:': 'Obiekt działa całodobowo, co czyni go idealnym przystankiem dla podróżnych o każdej porze. MOP oferuje:',
  
  'Situated at kilometer marker 399+000 on the A4 highway, the rest area is easily accessible for vehicles traveling in the Katowice direction.': 'Usytuowany przy słupku kilometrowym 399+000 na autostradzie A4, MOP jest łatwo dostępny dla pojazdów podróżujących w kierunku Katowic.',
  
  'The area is well-lit (with area lighting) and equipped with modern safety features.': 'Teren jest dobrze oświetlony (z oświetleniem terenu) i wyposażony w nowoczesne funkcje bezpieczeństwa.',
  
  'The area is well-lit (without area lighting) and equipped with modern safety features.': 'Teren jest dobrze oświetlony (bez oświetlenia terenu) i wyposażony w nowoczesne funkcje bezpieczeństwa.',
  
  'CCTV surveillance provides enhanced security. The area is fenced for added security.': 'Monitoring CCTV zapewnia zwiększone bezpieczeństwo. Teren jest ogrodzony dla dodatkowego bezpieczeństwa.',
  
  'The facility maintains high standards of cleanliness and safety for all visitors.': 'Obiekt utrzymuje wysokie standardy czystości i bezpieczeństwa dla wszystkich odwiedzających.',
};

// Pattern-based translations for dynamic content
const patternTranslations = [
  {
    pattern: /([^-]+) Rest Area - Highway Services in ([^$]+)$/gm,
    replacement: '$1 MOP - Usługi Drogowe w województwie $2'
  },
  {
    pattern: /Located on the ([A-Z0-9]+) highway at kilometer marker ([0-9+]+) in the ([^,]+) region, ([^,]+) offers essential services for travelers heading towards ([^.]+)\./gi,
    replacement: 'Położony na autostradzie $1 przy słupku kilometrowym $2 w województwie $3, MOP $4 oferuje niezbędne usługi dla podróżnych zmierzających w kierunku $5.'
  },
  {
    pattern: /Situated at kilometer marker ([0-9+]+) on the ([A-Z0-9]+) highway, the rest area is easily accessible for vehicles traveling in the ([^.]+) direction\./gi,
    replacement: 'Usytuowany przy słupku kilometrowym $1 na autostradzie $2, MOP jest łatwo dostępny dla pojazdów podróżujących w kierunku $3.'
  }
];

// Word/phrase translations
const translations = {
  'Facilities & Services': 'Udogodnienia i Usługi',
  'Location & Access': 'Lokalizacja i Dostęp',
  'Safety & Security': 'Bezpieczeństwo i Ochrona',
  'Additional Information': 'Dodatkowe Informacje',
  'Rest Area': 'MOP',
  'Highway Services': 'Usługi Drogowe',
  'The facility provides parking for:': 'Obiekt zapewnia parking dla:',
  'Cars:': 'Samochody:',
  'Trucks:': 'Ciężarówki:',
  'Buses:': 'Autobusy:',
  ' spaces': ' miejsc',
  '- Clean toilet facilities': '- Czyste toalety',
  '- Fuel station': '- Stacja paliw',
  '- Restaurant/Bistro': '- Restauracja/Bistro',
  '- Showers': '- Prysznice',
  '- EV charging station': '- Stacja ładowania pojazdów elektrycznych',
  '- **Last Verified:**': '- **Ostatnia weryfikacja:**',
  '- **Data Source:** [Link](': '- **Źródło danych:** [Link](',
  'Małopolskie': 'małopolskim',
};

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function translateText(text) {
  let translated = text;

  // Step 1: Apply complete sentence translations first
  Object.entries(completeSentenceTranslations).forEach(([english, polish]) => {
    const escapedEnglish = escapeRegExp(english);
    const regex = new RegExp(escapedEnglish, 'gi');
    translated = translated.replace(regex, polish);
  });

  // Step 2: Apply pattern-based translations
  patternTranslations.forEach(({ pattern, replacement }) => {
    translated = translated.replace(pattern, replacement);
  });

  // Step 3: Apply word/phrase translations
  Object.entries(translations).forEach(([english, polish]) => {
    const escapedEnglish = escapeRegExp(english);
    const regex = new RegExp(escapedEnglish, 'gi');
    translated = translated.replace(regex, polish);
  });

  return translated;
}

// Test cases
const testCases = [
  {
    name: "Title translation",
    input: "Aleksandrowice Rest Area - Highway Services in Małopolskie",
    expected: "Aleksandrowice MOP - Usługi Drogowe w województwie małopolskim"
  },
  {
    name: "Complete sentence translation",
    input: "The facility operates 24/7, making it an ideal stop for travelers at any time. The rest area features:",
    expected: "Obiekt działa całodobowo, co czyni go idealnym przystankiem dla podróżnych o każdej porze. MOP oferuje:"
  },
  {
    name: "Section headers",
    input: "#### Facilities & Services\n#### Location & Access\n#### Safety & Security",
    expected: "#### Udogodnienia i Usługi\n#### Lokalizacja i Dostęp\n#### Bezpieczeństwo i Ochrona"
  }
];

console.log('🧪 Testing improved translation logic...\n');

let allTestsPassed = true;

testCases.forEach((testCase, index) => {
  const result = translateText(testCase.input);
  const passed = result === testCase.expected;
  
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`Input:    "${testCase.input}"`);
  console.log(`Expected: "${testCase.expected}"`);
  console.log(`Result:   "${result}"`);
  console.log(`Status:   ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  if (!passed) {
    allTestsPassed = false;
  }
});

console.log(`\n📊 Test Summary: ${allTestsPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}`);

if (allTestsPassed) {
  console.log('\n🎉 Translation logic is working correctly. Ready to regenerate Polish files!');
} else {
  console.log('\n⚠️  Translation logic needs adjustment before regenerating files.');
}
