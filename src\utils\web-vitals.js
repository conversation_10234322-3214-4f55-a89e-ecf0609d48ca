/**
 * Local Web Vitals module
 * Optimized version that bundles with the application instead of loading from CDN
 */

import { onCLS, onFID, onFCP, onLCP, onTTFB, onINP } from 'web-vitals';

/**
 * Initialize Core Web Vitals tracking with Google Analytics
 * @param {Function} callback - Function to send metrics to analytics
 */
export function initWebVitals(callback) {
  if (typeof callback !== 'function') {
    console.warn('Web Vitals: Invalid callback function provided');
    return;
  }

  try {
    // Track Cumulative Layout Shift
    onCLS(callback);
    
    // Track First Input Delay
    onFID(callback);
    
    // Track First Contentful Paint
    onFCP(callback);
    
    // Track Largest Contentful Paint
    onLCP(callback);
    
    // Track Time to First Byte
    onTTFB(callback);
    
    // Track Interaction to Next Paint (newer metric, may not be available)
    if (onINP) {
      onINP(callback);
    }
    
    console.log('Web Vitals: Successfully initialized tracking');
  } catch (error) {
    console.warn('Web Vitals: Failed to initialize tracking:', error);
  }
}

/**
 * Format metric value for Google Analytics
 * @param {Object} metric - Web Vitals metric object
 * @returns {number} - Formatted value for GA
 */
export function formatMetricValue(metric) {
  // CLS values are typically small decimals, multiply by 1000 for better precision
  if (metric.name === 'CLS') {
    return Math.round(metric.value * 1000);
  }
  
  // Round other metrics to nearest integer
  return Math.round(metric.value);
}

/**
 * Create a standardized metric object for analytics
 * @param {Object} metric - Web Vitals metric object
 * @returns {Object} - Standardized metric for analytics
 */
export function createAnalyticsMetric(metric) {
  return {
    name: metric.name,
    value: formatMetricValue(metric),
    id: metric.id,
    navigationType: metric.navigationType || 'unknown',
    rating: metric.rating || 'unknown'
  };
}

/**
 * Send Web Vital metric to Google Analytics
 * @param {Object} metric - Web Vitals metric object
 */
export function sendWebVitalToGA(metric) {
  if (typeof gtag === 'undefined') {
    console.warn('Web Vitals: gtag not available, skipping metric:', metric.name);
    return;
  }

  const analyticsMetric = createAnalyticsMetric(metric);
  
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    event_label: analyticsMetric.id,
    value: analyticsMetric.value,
    non_interaction: true,
    custom_parameter_1: analyticsMetric.navigationType,
    custom_parameter_2: analyticsMetric.rating
  });
}

/**
 * Initialize Web Vitals with Google Analytics integration
 */
export function initWebVitalsGA() {
  initWebVitals(sendWebVitalToGA);
}

// Export individual metric functions for advanced usage
export { onCLS, onFID, onFCP, onLCP, onTTFB, onINP };
