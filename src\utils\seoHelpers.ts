// SEO utility functions for enhanced optimization

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  type?: 'website' | 'article' | 'place';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
}

// Generate optimized title tags with proper length and hierarchy
export function generateOptimizedTitle(
  pageTitle: string,
  siteTitle: string = 'stops24.com',
  separator: string = ' - '
): string {
  const maxLength = 60;
  const fullTitle = `${pageTitle}${separator}${siteTitle}`;
  
  if (fullTitle.length <= maxLength) {
    return fullTitle;
  }
  
  // Truncate page title if too long
  const availableLength = maxLength - separator.length - siteTitle.length;
  const truncatedPageTitle = pageTitle.length > availableLength 
    ? pageTitle.substring(0, availableLength - 3) + '...'
    : pageTitle;
    
  return `${truncatedPageTitle}${separator}${siteTitle}`;
}

// Generate optimized meta descriptions
export function generateOptimizedDescription(
  content: string,
  maxLength: number = 160
): string {
  if (content.length <= maxLength) {
    return content;
  }
  
  // Find the last complete sentence within the limit
  const truncated = content.substring(0, maxLength - 3);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return content.substring(0, lastSentence + 1);
  }
  
  // If no good sentence break, truncate at word boundary
  const lastSpace = truncated.lastIndexOf(' ');
  return content.substring(0, lastSpace) + '...';
}

// Generate SEO-friendly slugs
export function generateSEOSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Generate keywords from content
export function extractKeywords(
  title: string,
  description: string,
  content?: string
): string[] {
  const text = `${title} ${description} ${content || ''}`.toLowerCase();
  
  // Common stop words to exclude
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
    'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
    'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
  ]);
  
  // Extract words and filter
  const words = text
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word));
  
  // Count frequency and return top keywords
  const frequency: Record<string, number> = {};
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1;
  });
  
  return Object.entries(frequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);
}

// Generate FAQ structured data
export function generateFAQStructuredData(faqs: Array<{question: string, answer: string}>): object {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// Generate organization structured data
export function generateOrganizationStructuredData(): object {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "stops24.com",
    "description": "European Highway Rest Areas Directory",
    "url": "https://stops24.com",
    "logo": "https://stops24.com/favicon.svg",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://stops24.com/contact"
    },
    "sameAs": [
      "https://twitter.com/stops24",
      "https://facebook.com/stops24",
      "https://linkedin.com/company/stops24"
    ]
  };
}

// Generate local business structured data for rest areas
export function generateLocalBusinessStructuredData(
  restArea: any,
  url: string
): object {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": restArea.title,
    "description": restArea.description_short,
    "url": url,
    "image": restArea.featured_image,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": restArea.address_line,
      "addressCountry": "PL"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": restArea.coordinates.lat,
      "longitude": restArea.coordinates.lon
    },
    "priceRange": "$",
    "servesCuisine": "European",
    "amenityFeature": Object.entries(restArea.amenities)
      .filter(([_, available]) => available)
      .map(([amenity, _]) => ({
        "@type": "LocationFeatureSpecification",
        "name": amenity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))
  };
}

// Validate and optimize SEO configuration
export function validateSEOConfig(config: SEOConfig): {
  isValid: boolean;
  warnings: string[];
  suggestions: string[];
} {
  const warnings: string[] = [];
  const suggestions: string[] = [];
  
  // Title validation
  if (config.title.length > 60) {
    warnings.push('Title is longer than 60 characters');
  }
  if (config.title.length < 30) {
    suggestions.push('Consider making the title more descriptive (30+ characters)');
  }
  
  // Description validation
  if (config.description.length > 160) {
    warnings.push('Meta description is longer than 160 characters');
  }
  if (config.description.length < 120) {
    suggestions.push('Consider expanding the meta description (120+ characters)');
  }
  
  // Keywords validation
  if (config.keywords && config.keywords.length > 10) {
    warnings.push('Too many keywords - focus on 5-10 most relevant ones');
  }
  
  return {
    isValid: warnings.length === 0,
    warnings,
    suggestions
  };
}

// Generate hreflang tags for international SEO
export function generateHreflangTags(
  currentPath: string,
  supportedLocales: string[] = ['en', 'pl', 'de', 'fr']
): Array<{hreflang: string, href: string}> {
  return supportedLocales.map(locale => ({
    hreflang: locale,
    href: `https://stops24.com/${locale === 'en' ? '' : locale + '/'}${currentPath}`
  }));
}

// Calculate content readability score (simplified Flesch Reading Ease)
export function calculateReadabilityScore(text: string): {
  score: number;
  level: string;
  suggestions: string[];
} {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const words = text.split(/\s+/).filter(w => w.length > 0);
  const syllables = words.reduce((count, word) => {
    return count + countSyllables(word);
  }, 0);
  
  const avgSentenceLength = words.length / sentences.length;
  const avgSyllablesPerWord = syllables / words.length;
  
  // Simplified Flesch Reading Ease formula
  const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  
  let level: string;
  const suggestions: string[] = [];
  
  if (score >= 90) {
    level = 'Very Easy';
  } else if (score >= 80) {
    level = 'Easy';
  } else if (score >= 70) {
    level = 'Fairly Easy';
  } else if (score >= 60) {
    level = 'Standard';
  } else if (score >= 50) {
    level = 'Fairly Difficult';
    suggestions.push('Consider using shorter sentences and simpler words');
  } else if (score >= 30) {
    level = 'Difficult';
    suggestions.push('Break up long sentences and use more common words');
  } else {
    level = 'Very Difficult';
    suggestions.push('Significantly simplify the content for better readability');
  }
  
  return { score: Math.max(0, Math.min(100, score)), level, suggestions };
}

// Helper function to count syllables in a word
function countSyllables(word: string): number {
  word = word.toLowerCase();
  if (word.length <= 3) return 1;
  
  const vowels = 'aeiouy';
  let count = 0;
  let previousWasVowel = false;
  
  for (let i = 0; i < word.length; i++) {
    const isVowel = vowels.includes(word[i]);
    if (isVowel && !previousWasVowel) {
      count++;
    }
    previousWasVowel = isVowel;
  }
  
  // Handle silent 'e'
  if (word.endsWith('e')) {
    count--;
  }
  
  return Math.max(1, count);
}
