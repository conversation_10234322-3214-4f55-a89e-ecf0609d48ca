---
// SEO Monitoring Component
// This component adds essential tracking and monitoring for SEO performance

export interface Props {
  enableGoogleAnalytics?: boolean;
  enableGoogleSearchConsole?: boolean;
  enableBingWebmaster?: boolean;
  googleAnalyticsId?: string;
  googleSearchConsoleId?: string;
  bingWebmasterId?: string;
}

const {
  enableGoogleAnalytics = false,
  enableGoogleSearchConsole = false,
  enableBingWebmaster = false,
  googleAnalyticsId,
  googleSearchConsoleId,
  bingWebmasterId
} = Astro.props;

// Only include in production
const isProduction = import.meta.env.PROD;
---

{isProduction && (
  <>
    <!-- Google Analytics 4 -->
    {enableGoogleAnalytics && googleAnalyticsId && (
      <>
        <script async src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`}></script>
        <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '{googleAnalyticsId}', {
            page_title: document.title,
            page_location: window.location.href,
            content_group1: 'Directory',
            custom_map: {
              'dimension1': 'page_type',
              'dimension2': 'location_hierarchy'
            }
          });
          
          // Track SEO-relevant events
          gtag('event', 'page_view', {
            page_title: document.title,
            page_location: window.location.href,
            page_type: document.querySelector('meta[name="page-type"]')?.content || 'unknown'
          });
        </script>
      </>
    )}

    <!-- Google Search Console Verification -->
    {enableGoogleSearchConsole && googleSearchConsoleId && (
      <meta name="google-site-verification" content={googleSearchConsoleId} />
    )}

    <!-- Bing Webmaster Tools Verification -->
    {enableBingWebmaster && bingWebmasterId && (
      <meta name="msvalidate.01" content={bingWebmasterId} />
    )}

    <!-- Optimized Core Web Vitals Monitoring -->
    <script src="/js/web-vitals-ga.js" defer></script>

    <!-- SEO Performance Monitoring -->
    <script>
      // Monitor SEO-relevant user interactions
      document.addEventListener('DOMContentLoaded', function() {
        // Track internal link clicks
        document.addEventListener('click', function(e) {
          const link = e.target.closest('a');
          if (link && link.hostname === window.location.hostname) {
            if (typeof gtag !== 'undefined') {
              gtag('event', 'internal_link_click', {
                event_category: 'Navigation',
                event_label: link.href,
                value: 1
              });
            }
          }
        });

        // Track search interactions (if search functionality exists)
        const searchInputs = document.querySelectorAll('input[type="search"], input[name*="search"]');
        searchInputs.forEach(input => {
          input.addEventListener('input', function() {
            if (this.value.length > 2 && typeof gtag !== 'undefined') {
              gtag('event', 'search', {
                search_term: this.value,
                event_category: 'Site Search'
              });
            }
          });
        });

        // Track scroll depth for content engagement
        let maxScroll = 0;
        window.addEventListener('scroll', function() {
          const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
          if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
            maxScroll = scrollPercent;
            if (typeof gtag !== 'undefined') {
              gtag('event', 'scroll', {
                event_category: 'Engagement',
                event_label: `${scrollPercent}%`,
                value: scrollPercent
              });
            }
          }
        });
      });
    </script>

    <!-- Schema.org Validation Helper (Development) -->
    {import.meta.env.DEV && (
      <script>
        // Validate structured data in development
        console.group('🔍 SEO Debug Information');
        
        // Check for structured data
        const structuredData = document.querySelectorAll('script[type="application/ld+json"]');
        console.log(`📊 Found ${structuredData.length} structured data blocks`);
        
        structuredData.forEach((script, index) => {
          try {
            const data = JSON.parse(script.textContent);
            console.log(`📋 Structured Data ${index + 1}:`, data);
          } catch (e) {
            console.error(`❌ Invalid JSON in structured data ${index + 1}:`, e);
          }
        });

        // Check meta tags
        const metaTags = {
          title: document.title,
          description: document.querySelector('meta[name="description"]')?.content,
          canonical: document.querySelector('link[rel="canonical"]')?.href,
          ogTitle: document.querySelector('meta[property="og:title"]')?.content,
          ogDescription: document.querySelector('meta[property="og:description"]')?.content,
          ogImage: document.querySelector('meta[property="og:image"]')?.content,
          robots: document.querySelector('meta[name="robots"]')?.content
        };
        
        console.log('🏷️ Meta Tags:', metaTags);
        
        // Validate title length
        if (metaTags.title && metaTags.title.length > 60) {
          console.warn('⚠️ Title is longer than 60 characters:', metaTags.title.length);
        }
        
        // Validate description length
        if (metaTags.description && metaTags.description.length > 160) {
          console.warn('⚠️ Meta description is longer than 160 characters:', metaTags.description.length);
        }
        
        // Check for missing essential tags
        if (!metaTags.description) console.warn('⚠️ Missing meta description');
        if (!metaTags.canonical) console.warn('⚠️ Missing canonical URL');
        if (!metaTags.ogTitle) console.warn('⚠️ Missing Open Graph title');
        if (!metaTags.ogImage) console.warn('⚠️ Missing Open Graph image');
        
        console.groupEnd();
      </script>
    )}
  </>
)}

<!-- Performance Hints -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Additional SEO Meta Tags -->
<meta name="format-detection" content="telephone=no">
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="stops24">

<!-- Favicon and App Icons -->
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="manifest" href="/site.webmanifest">
<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#0ea5e9">
<meta name="msapplication-TileColor" content="#0ea5e9">

<style>
  /* Critical CSS for above-the-fold content */
  .page-transition-wrapper {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
  
  .page-transition-wrapper.page-loaded {
    opacity: 1;
  }
  
  /* Optimize font loading */
  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
</style>
