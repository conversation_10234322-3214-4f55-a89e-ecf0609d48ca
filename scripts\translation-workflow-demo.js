#!/usr/bin/env node

/**
 * Translation Workflow Demonstration
 * 
 * This script demonstrates the complete two-phase translation workflow
 * and creates sample files to show how the process works.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directory paths
const translationJsonDir = path.join(__dirname, '../src/content/rest-areas/translation-json');
const polishJsonDir = path.join(translationJsonDir, 'pl');
const consolidatedPolishJsonFile = path.join(polishJsonDir, 'all-content-for-translation.json');

/**
 * Create sample consolidated Polish JSON file for demonstration
 */
function createSampleConsolidatedPolishJson() {
  // Ensure directory exists
  if (!fs.existsSync(polishJsonDir)) {
    fs.mkdirSync(polishJsonDir, { recursive: true });
  }

  const sampleConsolidatedPolishContent = {
    "mop-aleksandrowice-a4.md": {
      frontmatter: {
        title: "Aleksandrowice MOP",
        description_short: "MOP Aleksandrowice na autostradzie A4 w Polsce.",
        administrator: "Koncesjonariusz- SAM",
        mop_category: "MOP funkcja komercyjna"
      },
      content: `### Aleksandrowice MOP - Usługi Drogowe w województwie małopolskim
Położony na autostradzie A4 przy słupku kilometrowym 399+000 w województwie małopolskim, MOP Aleksandrowice oferuje niezbędne usługi dla podróżnych zmierzających w kierunku Katowic. Obiekt stanowi wygodny punkt postojowy z różnymi udogodnieniami zapewniającymi komfortową podróż.

#### Udogodnienia i Usługi
Obiekt działa całodobowo, co czyni go idealnym przystankiem dla podróżnych o każdej porze. MOP oferuje:

- Czyste toalety
- Stacja paliw
- Restauracja/Bistro
- Prysznice
- Stacja ładowania pojazdów elektrycznych

#### Lokalizacja i Dostęp
Usytuowany przy słupku kilometrowym 399+000 na autostradzie A4, MOP jest łatwo dostępny dla pojazdów podróżujących w kierunku Katowic.

Obiekt zapewnia parking dla:
- Samochody: 75 miejsc
- Ciężarówki: 24 miejsc
- Autobusy: 4 miejsc

#### Bezpieczeństwo i Ochrona
Teren jest dobrze oświetlony (z oświetleniem terenu) i wyposażony w nowoczesne funkcje bezpieczeństwa.

Monitoring CCTV zapewnia zwiększone bezpieczeństwo. Teren jest ogrodzony dla dodatkowego bezpieczeństwa.
Obiekt utrzymuje wysokie standardy czystości i bezpieczeństwa dla wszystkich odwiedzających.

##### Dodatkowe Informacje

- **Ostatnia weryfikacja:** 2025-06-08
- **Źródło danych:** [Link](https://www.gov.pl/web/gddkia/wykaz-parkingow-i-mop)`
    },
    "mop-bagno-a4.md": {
      frontmatter: {
        title: "Bagno MOP",
        description_short: "MOP Bagno na autostradzie A4 w Polsce.",
        administrator: "GDDKiA",
        mop_category: "MOP funkcja podstawowa"
      },
      content: `### Bagno MOP - Usługi Drogowe w województwie małopolskim
Położony na autostradzie A4 przy słupku kilometrowym 470+350 w województwie małopolskim, MOP Bagno oferuje niezbędne usługi dla podróżnych zmierzających w kierunku Tarnowa. Obiekt stanowi wygodny punkt postojowy z różnymi udogodnieniami zapewniającymi komfortową podróż.

#### Udogodnienia i Usługi
Obiekt działa całodobowo, co czyni go idealnym przystankiem dla podróżnych o każdej porze. MOP oferuje:

- Czyste toalety
- Restauracja/Bistro
- Prysznice

#### Lokalizacja i Dostęp
Usytuowany przy słupku kilometrowym 470+350 na autostradzie A4, MOP jest łatwo dostępny dla pojazdów podróżujących w kierunku Tarnowa.

Obiekt zapewnia parking dla:
- Samochody: 56 miejsc
- Ciężarówki: 28 miejsc
- Autobusy: 0 miejsc

#### Bezpieczeństwo i Ochrona
Teren jest dobrze oświetlony (z oświetleniem terenu) i wyposażony w nowoczesne funkcje bezpieczeństwa.

Teren jest ogrodzony dla dodatkowego bezpieczeństwa.
Obiekt utrzymuje wysokie standardy czystości i bezpieczeństwa dla wszystkich odwiedzających.

##### Dodatkowe Informacje

- **Ostatnia weryfikacja:** 2025-06-08
- **Źródło danych:** [Link](https://www.gov.pl/web/gddkia/wykaz-parkingow-i-mop)`
    }
  };

  fs.writeFileSync(consolidatedPolishJsonFile, JSON.stringify(sampleConsolidatedPolishContent, null, 2), 'utf-8');

  return consolidatedPolishJsonFile;
}

/**
 * Main demonstration function
 */
function main() {
  console.log('🚀 Translation Workflow Demonstration');
  console.log('=====================================\n');

  console.log('📋 Two-Phase Translation Process Overview:');
  console.log('==========================================\n');

  console.log('🔄 Phase 1: Content Extraction (Consolidated)');
  console.log('   Purpose: Extract markdown content from English files for manual translation');
  console.log('   Script:  extract-content-for-translation.js');
  console.log('   Input:   src/content/rest-areas/en/*.md');
  console.log('   Output:  src/content/rest-areas/translation-json/en/all-content-for-translation.json');
  console.log('   Action:  Consolidates all content into single JSON file for easier translation\n');

  console.log('✍️  Manual Translation Step (Consolidated)');
  console.log('   Purpose: Human translator creates Polish version of consolidated JSON');
  console.log('   Input:   src/content/rest-areas/translation-json/en/all-content-for-translation.json');
  console.log('   Output:  src/content/rest-areas/translation-json/pl/all-content-for-translation.json');
  console.log('   Action:  Translator works with single file containing all content\n');

  console.log('🔄 Phase 2: Polish File Generation (From Consolidated)');
  console.log('   Purpose: Generate complete Polish markdown files from consolidated JSON');
  console.log('   Script:  generate-polish-from-json.js');
  console.log('   Input:   Consolidated Polish JSON + English markdown frontmatter');
  console.log('   Output:  src/content/rest-areas/pl/*.md');
  console.log('   Action:  Processes consolidated JSON to generate individual markdown files\n');

  console.log('🎯 Benefits of Consolidated Approach:');
  console.log('=====================================');
  console.log('✅ Eliminates language mixing completely');
  console.log('✅ Separates technical automation from human translation');
  console.log('✅ Single file workflow - easier for translators to manage');
  console.log('✅ Maintains consistent frontmatter structure');
  console.log('✅ Enables batch processing and progress tracking');
  console.log('✅ Provides clear audit trail of translations');
  console.log('✅ Reduces file management overhead');
  console.log('✅ Better version control for translation changes\n');

  console.log('📁 Creating sample consolidated Polish JSON file for demonstration...');

  try {
    const samplePath = createSampleConsolidatedPolishJson();
    console.log(`✅ Created sample file: ${path.relative(__dirname, samplePath)}\n`);

    console.log('🧪 Testing the generation process...');
    console.log('Run the following commands to test:');
    console.log('   # Generate all files from consolidated JSON:');
    console.log('   node generate-polish-from-json.js\n');
    console.log('   # Generate specific file from consolidated JSON:');
    console.log('   node generate-polish-from-json.js mop-aleksandrowice-a4.md\n');

    console.log('📚 Complete Consolidated Workflow Commands:');
    console.log('===========================================');
    console.log('1. Extract all content to consolidated JSON:');
    console.log('   node extract-content-for-translation.js\n');
    console.log('2. Manually translate consolidated JSON file:');
    console.log('   - Copy: en/all-content-for-translation.json → pl/all-content-for-translation.json');
    console.log('   - Translate all frontmatter and content fields to Polish\n');
    console.log('3. Generate all Polish markdown files:');
    console.log('   node generate-polish-from-json.js\n');
    console.log('4. Verify results and test multilingual functionality\n');

    console.log('🎉 Demonstration setup complete!');
    console.log('This approach will completely resolve the language mixing issues.');

  } catch (error) {
    console.error('❌ Error during demonstration setup:', error.message);
  }
}

main();
