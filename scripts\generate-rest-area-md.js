import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DATA_FILE = path.join(__dirname, '../data_set.csv');
const OUTPUT_DIR = path.join(__dirname, '../src/content/rest-areas/en');

// Helper function to slugify strings
function slugify(text) {
  return text
    .toString()
    .toLowerCase()
    .replace(/ł/g, 'l')
    .replace(/ć/g, 'c')
    .replace(/ó/g, 'o')
    .replace(/ę/g, 'e')
    .replace(/ą/g, 'a')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
}

// Helper function to format dates to YYYY-MM-DD
function formatDate(dateString) {
  let date;
  if (dateString) {
    date = new Date(dateString);
    if (isNaN(date.getTime())) { // Check if date is invalid
      date = new Date(); // Use current date if invalid
    }
  } else {
    date = new Date(); // Use current date if no dateString provided
  }
  return date.toISOString().split('T')[0];
}

// Function to parse CSV content
function parseCsv(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',').map(header => header.trim());
  const data = [];

  const csvRegex = /(?:^|,)(\"(?:[^\"])*\"|[^,]*)/g;

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    const values = [];
    let match;
    while ((match = csvRegex.exec(line)) !== null) {
      let value = match[1];
      if (value.startsWith('"') && value.endsWith('"')) {
        value = value.substring(1, value.length - 1);
      }
      values.push(value.trim());
    }

    const row = {};
    for (let j = 0; j < headers.length; j++) {
      row[headers[j]] = values[j];
    }
    data.push(row);
  }
  return data;
}

// Function to generate YAML frontmatter
function generateFrontmatter(data) {
  // Placeholder for frontmatter generation logic
  // This will be implemented based on the detailed plan
  return `
title: "${data.rest_area_id} Rest Area"
publishedDate: ${formatDate(data.last_verified_date)}
description_short: "Rest area ${data.rest_area_id} on ${data.road_class}${data.road_number} road in Poland."
address_line: "${data.road_class}${data.road_number}, ${data.km_marker}, ${data.location}, ${data.region}"
coordinates:
  lat: ${data.longitude}
  lon: ${data.latitude}
work_hours: '{"monday": "00:00-24:00", "tuesday": "00:00-24:00", "wednesday": "00:00-24:00", "thursday": "00:00-24:00", "friday": "00:00-24:00", "saturday": "00:00-24:00", "sunday": "00:00-24:00"}'
contact_info: '{"phone": "", "email": ""}'
maps_url: "https://maps.google.com/?q=${data.longitude},${data.latitude}"
rating: 0
amenities:
    toilets: ${data.toilets_available === 'yes'}
    wifi: false
    fuel_station: ${data.gas_station_available === 'yes'}
    restaurant: ${data.restaurant_bistro_available === 'yes'}
    shop: false
    playground: false
    showers: ${data.showers_available === 'yes'}
    car_wash: ${data.car_wash_available === 'yes'}
    ev_charging: ${data.ev_charging_station === 'yes'}
    security: ${data.security_personnel_on_site === 'yes'}
    cctv: ${data.cctv_video_surveillance === 'yes'}
    lighting: ${data.area_lighting === 'yes'}
    accommodation: ${data.accommodation_available === 'yes'}
    fenced_area: ${data.fenced_area === 'yes'}
featured_image: "https://placehold.co/600x400?text=${encodeURIComponent(data.rest_area_id)}"
country_code: "${data.country_code || 'PL'}"
location_path: "${slugify(data.country)}/${slugify(data.region)}/${slugify(data.location)}"
highway_tag: "${data.road_class}${data.road_number}"
administrator: "${data.administrator || ''}"
mop_category: "${data.mop_category || ''}"
road_class: "${data.road_class || ''}"
road_number: "${data.road_number || ''}"
km_marker: "${data.km_marker || ''}"
travel_direction: "${data.travel_direction || ''}"
parking_spaces_cars: ${data.parking_spaces_cars || 0}
parking_spaces_trucks: ${data.parking_spaces_trucks_tir || 0}
parking_spaces_buses: ${data.parking_spaces_buses || 0}
security_personnel_on_site: ${data.security_personnel_on_site === 'yes'}
last_verified_date: ${formatDate(data.last_verified_date)}
data_source_url: "${data.data_source_url}"
`;
}

// Function to generate markdown content body
function generateContent(data) {
  // Placeholder for content generation logic
  // This will be implemented based on the detailed plan
  return `
### ${data.rest_area_id} Rest Area - Highway Services in ${data.region}
Located on the ${data.road_class}${data.road_number} highway at kilometer marker ${data.km_marker} in the ${data.region} region, ${data.location} offers essential services for travelers heading towards ${data.travel_direction}. This facility provides a convenient stopping point with various amenities to ensure a comfortable journey.

#### Facilities & Services
The facility operates 24/7, making it an ideal stop for travelers at any time. The rest area features:

${data.toilets_available === 'yes' ? '- Clean toilet facilities\n' : ''}\
${data.gas_station_available === 'yes' ? '- Fuel station\n' : ''}\
${data.restaurant_bistro_available === 'yes' ? '- Restaurant/Bistro\n' : ''}\
${data.showers_available === 'yes' ? '- Showers\n' : ''}\
${data.car_wash_available === 'yes' ? '- Car wash\n' : ''}\
${data.ev_charging_station === 'yes' ? '- EV charging station\n' : ''}\
${data.wifi === 'yes' ? '- Wi-Fi access\n' : ''}\
${data.shop === 'yes' ? '- Shop\n' : ''}\
${data.playground === 'yes' ? '- Playground\n' : ''}\
${data.accommodation_available === 'yes' ? '- Accommodation\n' : ''}\

#### Location & Access
Situated at kilometer marker ${data.km_marker} on the ${data.road_class}${data.road_number} highway, the rest area is easily accessible for vehicles traveling in the ${data.travel_direction} direction. 

The facility provides parking for:
- Cars: ${data.parking_spaces_cars} spaces
- Trucks: ${data.parking_spaces_trucks_tir} spaces
- Buses: ${data.parking_spaces_buses} spaces

#### Safety & Security
The area is well-lit (${data.area_lighting === 'yes' ? 'with area lighting' : 'without area lighting'}) and equipped with modern safety features.

${data.cctv_video_surveillance === 'yes' ? 'CCTV surveillance provides enhanced security. ' : ''}\
${data.security_personnel_on_site === 'yes' ? 'Security personnel are available on-site. ' : ''}\
${data.fenced_area === 'yes' ? 'The area is fenced for added security. ' : ''}\

The facility maintains high standards of cleanliness and safety for all visitors.

##### Additional Information

- **Last Verified:** ${data.last_verified_date || new Date().toISOString().slice(0, 10)}
- **Data Source:** [Link](${data.data_source_url || '#'})

`;
}

async function main() {
  try {
    const csvContent = fs.readFileSync(DATA_FILE, 'utf-8');
    const data = parseCsv(csvContent);

    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    const generatedSlugs = {}; // To keep track of generated slugs and their counts

    for (const row of data) {
      let baseSlug = slugify(`${row.rest_area_id}-${row.road_class}${row.road_number}`);
      let currentSlug = baseSlug;
      let counter = 1;

      // Check for duplicates and append a number if necessary
      while (generatedSlugs[currentSlug]) {
        counter++;
        currentSlug = `${baseSlug}-${counter}`;
      }
      generatedSlugs[currentSlug] = true; // Mark this slug as used

      const filename = `mop-${currentSlug}.md`;
      const outputPath = path.join(OUTPUT_DIR, filename);

      const frontmatter = generateFrontmatter(row);
      const content = generateContent(row);

      const fileContent = `---\n${frontmatter.trim()}\n---\n${content.trim()}\n`;

      fs.writeFileSync(outputPath, fileContent, 'utf-8');
      console.log(`Generated: ${outputPath}`);
    }
    console.log('All markdown files generated successfully!');
  } catch (error) {
    console.error('Error generating markdown files:', error);
  }
}

main();