---
/**
 * Footer Ad Component
 * Optimized ad placement for footer area
 */

import AdUnit from '../AdUnit.astro';

export interface Props {
  adSlot?: string;
  showLabel?: boolean;
  className?: string;
}

const {
  adSlot = '4567890123', // Default placeholder - replace with actual ad slot
  showLabel = true,
  className = ''
} = Astro.props;
---

<div class={`footer-ad-container ${className}`}>
  <AdUnit
    adSlot={adSlot}
    format="horizontal"
    placement="footer"
    responsive={true}
    lazy={true}
    className="footer-ad"
    label={showLabel ? "Advertisement" : undefined}
  />
</div>

<style>
  .footer-ad-container {
    /* Footer ad container */
    width: 100%;
    border-top: 1px solid #e5e7eb;
    padding-top: 2rem;
    margin-top: 2rem;
    background: #f9fafb;
  }
  
  .dark .footer-ad-container {
    border-top-color: #374151;
    background: #111827;
  }
  
  .footer-ad {
    /* Footer ad styling */
    max-width: 728px;
    margin: 0 auto;
  }
  
  @media (max-width: 768px) {
    .footer-ad-container {
      padding-top: 1rem;
      margin-top: 1rem;
    }
  }
</style>
