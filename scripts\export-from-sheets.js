#!/usr/bin/env node

/**
 * Google Sheets to JSON Export Script
 * 
 * This script fetches data from Google Sheets and converts it to JSON format
 * for processing by the markdown generation script. Supports multiple languages
 * and validates data structure against the existing schema.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { fetchMultipleSheets, validateSheetStructure } from '../src/utils/googleSheetsClient.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  spreadsheetId: process.env.GOOGLE_SHEETS_ID || '',
  outputDir: path.join(__dirname, '../temp/sheets-export'),
  sheets: {
    en: {
      range: 'English!A:AZ', // English sheet
      language: 'en'
    }
    // Note: Polish sheet doesn't exist in this spreadsheet
  },
  // Required columns based on actual sheet structure
  requiredColumns: [
    'rest_area_id',
    'latitude', // Note: has leading space in sheet
    'longitude', // Note: has leading space in sheet
    'road_class',
    'road_number',
    'km_marker',
    'region',
    'location',
    'country'
  ],
  // Optional columns that will be included if present (based on actual sheet)
  optionalColumns: [
    'branch',
    'administrator',
    'mop_category',
    'travel_direction',
    'parking_spaces_cars',
    'parking_spaces_trucks',
    'parking_spaces_buses',
    'adr_vehicle_spaces_count',
    'toilets_available',
    'toilets_accessible',
    'gas_station_available',
    'restaurant',
    'accommodation_available',
    'ev_charging',
    'ev_charger_details',
    'security_personnel_on_site'
  ]
};

// Helper function to normalize column names
function normalizeColumnName(name) {
  return name.toString().trim().toLowerCase().replace(/\s+/g, '_');
}

// Convert sheet data to JSON objects
function convertSheetToJson(sheetData, language) {
  if (!sheetData || sheetData.length < 2) {
    console.warn(`No data rows found for language: ${language}`);
    return [];
  }

  const headers = sheetData[0].map(normalizeColumnName);
  const rows = sheetData.slice(1);
  
  return rows.map((row, index) => {
    const obj = { language };
    
    headers.forEach((header, colIndex) => {
      const value = row[colIndex] || '';
      obj[header] = value.toString().trim();
    });
    
    // Add row number for debugging
    obj._row_number = index + 2; // +2 because we start from row 2 (after headers)
    
    return obj;
  }).filter(obj => {
    // Filter out empty rows (rows where rest_area_id is empty)
    return obj.rest_area_id && obj.rest_area_id.trim() !== '';
  });
}

// Validate data against schema requirements
function validateData(data, language) {
  const errors = [];
  const warnings = [];
  
  data.forEach((item, index) => {
    const rowNum = item._row_number || index + 1;
    
    // Check required fields (using normalized column names)
    const requiredNormalized = CONFIG.requiredColumns.map(col => col.toLowerCase().replace(/\s+/g, '_'));
    requiredNormalized.forEach(col => {
      if (!item[col] || item[col].trim() === '') {
        errors.push(`Row ${rowNum}: Missing required field '${col}'`);
      }
    });

    // Validate coordinates (handle both normalized and original names)
    const lat = item.latitude || item['_latitude'] || '';
    const lon = item.longitude || item['_longitude'] || '';

    if (lat && isNaN(parseFloat(lat))) {
      errors.push(`Row ${rowNum}: Invalid latitude value '${lat}'`);
    }
    if (lon && isNaN(parseFloat(lon))) {
      errors.push(`Row ${rowNum}: Invalid longitude value '${lon}'`);
    }
    
    // Validate boolean fields
    const booleanFields = [
      'toilets_available', 'wifi', 'gas_station_available', 'restaurant_bistro_available',
      'shop', 'playground', 'showers_available', 'car_wash_available', 'ev_charging_station',
      'security_personnel_on_site', 'cctv_video_surveillance', 'area_lighting',
      'accommodation_available', 'fenced_area', 'toilets_accessible'
    ];
    
    booleanFields.forEach(field => {
      if (item[field] && !['yes', 'no', 'true', 'false', '1', '0'].includes(item[field].toLowerCase())) {
        warnings.push(`Row ${rowNum}: Field '${field}' should be yes/no, true/false, or 1/0. Got: '${item[field]}'`);
      }
    });
  });
  
  return { errors, warnings };
}

// Main export function
async function exportFromSheets() {
  try {
    console.log('🚀 Starting Google Sheets export...');
    
    // Validate configuration
    if (!CONFIG.spreadsheetId) {
      throw new Error('GOOGLE_SHEETS_ID environment variable is required');
    }
    
    // Ensure output directory exists
    await fs.mkdir(CONFIG.outputDir, { recursive: true });
    
    // Prepare sheet configurations
    const sheetConfigs = Object.entries(CONFIG.sheets).map(([lang, config]) => ({
      spreadsheetId: CONFIG.spreadsheetId,
      range: config.range,
      language: lang
    }));
    
    console.log('📊 Fetching data from Google Sheets...');
    const sheetsData = await fetchMultipleSheets(sheetConfigs);
    
    const exportResults = {
      timestamp: new Date().toISOString(),
      languages: {},
      summary: {
        totalRecords: 0,
        errors: [],
        warnings: []
      }
    };
    
    // Process each language
    for (const [language, sheetData] of Object.entries(sheetsData)) {
      console.log(`\n🔄 Processing ${language.toUpperCase()} data...`);
      
      // Validate sheet structure
      const validation = await validateSheetStructure(
        CONFIG.spreadsheetId,
        CONFIG.sheets[language].range,
        CONFIG.requiredColumns
      );
      
      if (!validation.isValid) {
        const error = `Sheet ${language} is missing required columns: ${validation.missingColumns.join(', ')}`;
        console.error(`❌ ${error}`);
        exportResults.summary.errors.push(error);
        continue;
      }
      
      // Convert to JSON
      const jsonData = convertSheetToJson(sheetData, language);
      console.log(`📝 Converted ${jsonData.length} records for ${language}`);
      
      // Validate data
      const dataValidation = validateData(jsonData, language);
      
      if (dataValidation.errors.length > 0) {
        console.error(`❌ Data validation errors for ${language}:`);
        dataValidation.errors.forEach(error => console.error(`   ${error}`));
        exportResults.summary.errors.push(...dataValidation.errors);
      }
      
      if (dataValidation.warnings.length > 0) {
        console.warn(`⚠️  Data validation warnings for ${language}:`);
        dataValidation.warnings.forEach(warning => console.warn(`   ${warning}`));
        exportResults.summary.warnings.push(...dataValidation.warnings);
      }
      
      // Save JSON file
      const outputFile = path.join(CONFIG.outputDir, `rest-areas-${language}.json`);
      await fs.writeFile(outputFile, JSON.stringify(jsonData, null, 2), 'utf-8');
      console.log(`💾 Saved: ${outputFile}`);
      
      // Update results
      exportResults.languages[language] = {
        recordCount: jsonData.length,
        outputFile: outputFile,
        errors: dataValidation.errors.length,
        warnings: dataValidation.warnings.length
      };
      
      exportResults.summary.totalRecords += jsonData.length;
    }
    
    // Save export summary
    const summaryFile = path.join(CONFIG.outputDir, 'export-summary.json');
    await fs.writeFile(summaryFile, JSON.stringify(exportResults, null, 2), 'utf-8');
    
    console.log('\n📋 Export Summary:');
    console.log(`   Total records: ${exportResults.summary.totalRecords}`);
    console.log(`   Languages: ${Object.keys(exportResults.languages).join(', ')}`);
    console.log(`   Errors: ${exportResults.summary.errors.length}`);
    console.log(`   Warnings: ${exportResults.summary.warnings.length}`);
    
    if (exportResults.summary.errors.length > 0) {
      console.error('\n❌ Export completed with errors. Please review the data before proceeding.');
      process.exit(1);
    } else {
      console.log('\n✅ Export completed successfully!');
    }
    
  } catch (error) {
    console.error('💥 Export failed:', error.message);
    process.exit(1);
  }
}

// Run the export if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  exportFromSheets();
}

export { exportFromSheets, CONFIG };
