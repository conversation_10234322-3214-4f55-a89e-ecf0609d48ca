# Multilingual Support for stops24.com

This document provides an overview of the internationalization (i18n) implementation for the stops24.com application, which supports English and Polish languages with localized URLs and comprehensive translation coverage.

## 🌍 Overview

The stops24.com application now supports:

- **English** (default): Clean URLs without language prefix
- **Polish**: URLs with `/pl/` prefix and localized slugs (`/pl/polska/` instead of `/pl/poland/`)
- **Automatic language detection** from URL, localStorage, and browser settings
- **Seamless language switching** that preserves page context
- **SEO-optimized localized URLs** for better search engine visibility

## 🚀 Quick Start

### Using Translations in Components

```astro
---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);
---

<h1>{t('homepage.title')}</h1>
<p>{t('homepage.subtitle')}</p>
```

### Adding the Language Selector

```astro
---
import LanguageSelector from '../components/LanguageSelector.astro';
---

<header>
  <nav><!-- navigation --></nav>
  <LanguageSelector />
</header>
```

## 🔗 URL Structure

| Page Type | English | Polish |
|-----------|---------|--------|
| Homepage | `/` | `/pl/` |
| Poland Browse | `/poland/` | `/pl/polska/` |
| Region Page | `/poland/mazowieckie/` | `/pl/polska/mazowieckie/` |
| Rest Areas | `/rest-areas/` | `/pl/rest-areas/` |

## 📁 Key Files

### Translation Files

- `src/i18n/en.json` - English translations
- `src/i18n/pl.json` - Polish translations
- `src/i18n/utils.ts` - i18n utility functions

### Components

- `src/components/LanguageSelector.astro` - Language switching component
- `src/layouts/Layout.astro` - Main layout with language detection

### Page Structure

```text
src/pages/
├── index.astro              # English homepage
├── poland/                  # English Poland pages
│   ├── index.astro
│   └── [...locationSlug].astro
└── pl/                      # Polish pages
    ├── index.astro          # Polish homepage
    ├── polska/              # Polish Poland pages (localized)
    │   ├── index.astro
    │   └── [...locationSlug].astro
    └── rest-areas/
        └── index.astro
```

## 🛠️ Common Tasks

### Adding a New Translation

1. Add to `src/i18n/en.json`:

```json
{
  "section": {
    "newKey": "English text"
  }
}
```

2. Add to `src/i18n/pl.json`:

```json
{
  "section": {
    "newKey": "Polish text"
  }
}
```

3. Use in component:
```astro
<p>{t('section.newKey')}</p>
```

### Creating a Localized Page

1. Create English version in `src/pages/`
2. Create Polish version in `src/pages/pl/`
3. Use translations for all text content
4. Update navigation links if needed

## 🧪 Testing

### Manual Testing Checklist

- [ ] All pages load in both languages
- [ ] Language switching preserves page context
- [ ] URLs use correct localized slugs
- [ ] Navigation works in both languages
- [ ] Search functionality works in both languages

### URL Testing

```bash
# Test English URLs
curl -I http://localhost:4321/poland/

# Test Polish URLs
curl -I http://localhost:4321/pl/polska/

# Verify old URLs return 404
curl -I http://localhost:4321/pl/poland/  # Should be 404
```

## 📚 Documentation

For detailed information, see:

- **[Complete Implementation Guide](docs/i18n-implementation.md)** - Comprehensive technical documentation
- **[Quick Reference Guide](docs/i18n-quick-reference.md)** - Developer quick reference

## 🔧 Configuration

### Astro Configuration

```javascript
// astro.config.mjs
export default defineConfig({
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'pl'],
    routing: {
      prefixDefaultLocale: false
    }
  }
});
```

### Supported Languages

- `en` - English (default)
- `pl` - Polish

## 🎯 Key Features

### Language Detection

1. **URL-based**: Primary detection from URL path
2. **localStorage**: Persists user preference
3. **Browser locale**: Fallback to browser language
4. **Default**: English as ultimate fallback

### URL Localization

- **Smart mapping**: `poland` ↔ `polska` for SEO
- **Context preservation**: Language switching maintains page location
- **Conflict avoidance**: Separate country pages from language locales

### Translation System

- **Hierarchical keys**: Organized with dot notation (`nav.home`)
- **Fallback support**: Missing translations fall back to English
- **Type safety**: TypeScript support for translation keys

## 🚀 Future Enhancements

The architecture supports easy addition of new languages:

1. Add locale to Astro config
2. Create translation file
3. Create page structure
4. Update type definitions

Example for German:

```javascript
// astro.config.mjs
i18n: {
  locales: ['en', 'pl', 'de']
}
```

## 🐛 Troubleshooting

### Common Issues

**Missing translations showing as keys:**
- Add the translation key to all language files

**Language switching not working:**
- Check `getLocalizedPath` function implementation
- Verify URL structure matches expected patterns

**404 errors on Polish pages:**
- Ensure pages exist in `src/pages/pl/` directory
- Check file naming and structure

**Wrong URLs after language switching:**
- Verify URL mapping in `getLocalizedPath` function
- Check for typos in URL patterns

## 📞 Support

For questions about the i18n implementation:
1. Check the [Complete Implementation Guide](docs/i18n-implementation.md)
2. Review the [Quick Reference Guide](docs/i18n-quick-reference.md)
3. Test with the provided URL examples
4. Verify translation file structure

## 📈 Performance

The i18n implementation is designed for optimal performance:
- **Static generation**: All pages are pre-built at build time
- **Minimal runtime**: Language detection happens once per page load
- **Efficient routing**: No runtime URL rewriting
- **SEO optimized**: Clean, localized URLs for search engines

---

**Implementation Status:** ✅ Complete
**Languages Supported:** English (en), Polish (pl)
**Last Updated:** May 2025
