// Enhanced page transitions with performance optimizations
document.addEventListener('astro:page-load', () => {
  // Add animation classes to elements with data-animate attribute
  const animatedElements = document.querySelectorAll('[data-animate]');

  // Batch DOM operations to reduce reflows
  if (animatedElements.length > 0) {
    requestAnimationFrame(() => {
      animatedElements.forEach((element, index) => {
        const animationType = element.getAttribute('data-animate');
        const delay = element.getAttribute('data-delay') || index * 100;

        // Use CSS custom properties instead of direct style manipulation
        element.style.setProperty('--animation-delay', `${delay}ms`);

        // Batch class additions
        setTimeout(() => {
          element.classList.add(animationType, 'animated');
        }, 10);
      });
    });
  }

  // Optimized parallax effect with throttling and will-change
  const parallaxElements = document.querySelectorAll('[data-parallax]');

  if (parallaxElements.length > 0) {
    let ticking = false;

    // Prepare elements for GPU acceleration
    parallaxElements.forEach(element => {
      element.style.willChange = 'transform';
    });

    const handleParallax = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;

          parallaxElements.forEach(element => {
            const speed = parseFloat(element.getAttribute('data-parallax')) || 0.1;
            const yPos = -(scrollY * speed);
            element.style.transform = `translate3d(0, ${yPos}px, 0)`;
          });

          ticking = false;
        });
        ticking = true;
      }
    };

    // Use passive listener for better performance
    window.addEventListener('scroll', handleParallax, { passive: true });
  }
  
  // Optimized smooth scroll for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');

  anchorLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();

      const targetId = link.getAttribute('href');
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        // Use scrollIntoView for better performance and browser optimization
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
  
  // Optimized page transition effects
  const pageTransitionElements = document.querySelectorAll('[data-page-transition]');

  if (pageTransitionElements.length > 0) {
    // Batch DOM operations
    requestAnimationFrame(() => {
      pageTransitionElements.forEach(element => {
        const transitionType = element.getAttribute('data-page-transition');
        element.classList.add(`transition-${transitionType}`);
      });
    });
  }
});

// Optimized navigation events handling
document.addEventListener('astro:before-preparation', ({ from, to, direction }) => {
  // Store navigation direction in sessionStorage for better performance
  if (from && to) {
    const fromPath = new URL(from).pathname;
    const toPath = new URL(to).pathname;

    // Determine navigation direction based on path depth
    const fromDepth = fromPath.split('/').filter(Boolean).length;
    const toDepth = toPath.split('/').filter(Boolean).length;

    let navDirection = 'same';

    if (toDepth > fromDepth) {
      navDirection = 'deeper';
    } else if (toDepth < fromDepth) {
      navDirection = 'shallower';
    }

    sessionStorage.setItem('navigationDirection', navDirection);
  }
});

// Apply direction-specific transitions with optimized DOM manipulation
document.addEventListener('astro:page-load', () => {
  const navDirection = sessionStorage.getItem('navigationDirection');

  if (navDirection) {
    // Use requestAnimationFrame to batch DOM operations
    requestAnimationFrame(() => {
      document.documentElement.setAttribute('data-navigation', navDirection);

      // Clean up after transition completes
      setTimeout(() => {
        sessionStorage.removeItem('navigationDirection');
        document.documentElement.removeAttribute('data-navigation');
      }, 1000);
    });
  }
});
